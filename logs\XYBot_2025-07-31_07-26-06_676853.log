2025-07-31 07:26:08 | SUCCESS | 读取主设置成功
2025-07-31 07:26:08 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-31 07:26:08 | INFO | 2025/07/31 07:26:08 GetRedisAddr: 127.0.0.1:6379
2025-07-31 07:26:08 | INFO | 2025/07/31 07:26:08 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-31 07:26:08 | INFO | 2025/07/31 07:26:08 Server start at :9000
2025-07-31 07:26:08 | SUCCESS | WechatAPI服务已启动
2025-07-31 07:26:09 | SUCCESS | 获取到登录uuid: gdxtFj6Ey-WiyYjB6OLF
2025-07-31 07:26:09 | INFO | 等待登录中，过期倒计时：240
2025-07-31 07:26:17 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-31 07:26:17 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-31 07:26:17 | SUCCESS | 登录成功
2025-07-31 07:26:17 | SUCCESS | 已开启自动心跳
2025-07-31 07:26:17 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-31 07:26:17 | SUCCESS | 数据库初始化成功
2025-07-31 07:26:17 | SUCCESS | 定时任务已启动
2025-07-31 07:26:17 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-31 07:26:17 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-31 07:26:18 | INFO | 播客API初始化成功
2025-07-31 07:26:18 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-31 07:26:18 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-31 07:26:18 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-31 07:26:18 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-31 07:26:18 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-31 07:26:18 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-31 07:26:18 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-31 07:26:18 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-31 07:26:18 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-31 07:26:18 | INFO | [ChatSummary] 数据库初始化成功
2025-07-31 07:26:19 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-07-31 07:26:19 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-07-31 07:26:19 | DEBUG |   - 启用状态: True
2025-07-31 07:26:19 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-07-31 07:26:19 | DEBUG |   - 设备ID: 7468716989062841895
2025-07-31 07:26:19 | DEBUG |   - Web ID: 7468716986638386703
2025-07-31 07:26:19 | DEBUG |   - Cookies配置: 已配置
2025-07-31 07:26:19 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-07-31 07:26:19 | DEBUG |   - 自然化响应: True
2025-07-31 07:26:19 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-31 07:26:19 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-31 07:26:19 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-31 07:26:19 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-31 07:26:19 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-31 07:26:19 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-31 07:26:19 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-31 07:26:19 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-31 07:26:19 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-31 07:26:19 | INFO | [RenameReminder] 开始启用插件...
2025-07-31 07:26:19 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-31 07:26:19 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-31 07:26:19 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-31 07:26:19 | INFO | 已设置检查间隔为 3600 秒
2025-07-31 07:26:19 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-31 07:26:19 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-31 07:26:20 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-31 07:26:20 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-31 07:26:20 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-31 07:26:21 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-31 07:26:21 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-31 07:26:21 | INFO | [yuanbao] 插件初始化完成
2025-07-31 07:26:21 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-31 07:26:21 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-31 07:26:21 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-31 07:26:21 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-31 07:26:21 | INFO | 处理堆积消息中
2025-07-31 07:26:24 | DEBUG | 接受到 22 条消息
2025-07-31 07:26:26 | SUCCESS | 处理堆积消息完毕
2025-07-31 07:26:26 | SUCCESS | 开始处理消息
2025-07-31 07:26:30 | DEBUG | 收到消息: {'MsgId': 1985223974, 'FromUserName': {'string': 'weixin'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '\n\t\t\t\t<sysmsg type="ClientCheckGetExtInfo">\n\t\t\t\t\t<ClientCheckGetExtInfo>\n\t\t\t\t\t\t<ReportContext>539033600</ReportContext>\n\t\t\t\t\t\t<Basic>0</Basic>\n                        <Cellular>1</Cellular>\n\t\t\t\t\t</ClientCheckGetExtInfo>\n\t\t\t\t</sysmsg>\n\t\t\t'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753917992, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4213797103658606291, 'MsgSeq': 871413170}
2025-07-31 07:26:30 | DEBUG | 系统消息类型: ClientCheckGetExtInfo
2025-07-31 07:29:32 | DEBUG | 收到消息: {'MsgId': 991592800, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'gjuse11:\n666'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753918174, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_3pKBWt9a|v1_sGoKmokD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Jayson先森 : 666', 'NewMsgId': 4075158409182604787, 'MsgSeq': 871413171}
2025-07-31 07:29:32 | INFO | 收到文本消息: 消息ID:991592800 来自:47325400669@chatroom 发送人:gjuse11 @:[] 内容:666
2025-07-31 07:29:32 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-31 07:29:32 | DEBUG | 处理消息内容: '666'
2025-07-31 07:29:32 | DEBUG | 消息内容 '666' 不匹配任何命令，忽略
2025-07-31 07:29:36 | DEBUG | 收到消息: {'MsgId': 1362465141, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_yby6o1jbfqyd12:\n获取女大图失败，请稍后再试！'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753918177, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_gx8CVhrL|v1_+yUJhj0X</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'DPbot : 获取女大图失败，请稍后再试！', 'NewMsgId': 6427758881607120718, 'MsgSeq': 871413172}
2025-07-31 07:29:36 | INFO | 收到文本消息: 消息ID:1362465141 来自:47325400669@chatroom 发送人:wxid_yby6o1jbfqyd12 @:[] 内容:获取女大图失败，请稍后再试！
2025-07-31 07:29:36 | DEBUG | 处理消息内容: '获取女大图失败，请稍后再试！'
2025-07-31 07:29:36 | DEBUG | 消息内容 '获取女大图失败，请稍后再试！' 不匹配任何命令，忽略
2025-07-31 07:29:46 | DEBUG | 收到消息: {'MsgId': 511875036, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="6868726c6c69656f79626363766e6965" encryver="0" cdnthumbaeskey="6868726c6c69656f79626363766e6965" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02045533949d0204688aaaeb042464386365313636302d373634322d343036332d623938322d3064623466616530363232350204052828010201000405004c4d360054381738" cdnthumblength="3258" cdnthumbheight="66" cdnthumbwidth="100" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02045533949d0204688aaaeb042464386365313636302d373634322d343036332d623938322d3064623466616530363232350204052828010201000405004c4d360054381738" length="24721" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02045533949d0204688aaaeb042464386365313636302d373634322d343036332d623938322d3064623466616530363232350204052828010201000405004c4d360054381738" hdlength="104218" md5="a0268fa5d89cab9780f701d0322232be">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753918187, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>37888b81ad47500d54fb7aa96f5919d9_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_gXQ+Qa43|v1_lSGkWsX2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德在群聊中发了一张图片', 'NewMsgId': 4184797256147996870, 'MsgSeq': 871413173}
2025-07-31 07:29:46 | INFO | 收到图片消息: 消息ID:511875036 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 XML:<?xml version="1.0"?><msg><img aeskey="6868726c6c69656f79626363766e6965" encryver="0" cdnthumbaeskey="6868726c6c69656f79626363766e6965" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02045533949d0204688aaaeb042464386365313636302d373634322d343036332d623938322d3064623466616530363232350204052828010201000405004c4d360054381738" cdnthumblength="3258" cdnthumbheight="66" cdnthumbwidth="100" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02045533949d0204688aaaeb042464386365313636302d373634322d343036332d623938322d3064623466616530363232350204052828010201000405004c4d360054381738" length="24721" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02045533949d0204688aaaeb042464386365313636302d373634322d343036332d623938322d3064623466616530363232350204052828010201000405004c4d360054381738" hdlength="104218" md5="a0268fa5d89cab9780f701d0322232be"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 07:29:46 | INFO | [ImageEcho] 保存图片信息成功，当前群 47325400669@chatroom 已存储 5 张图片
2025-07-31 07:29:46 | INFO | [TimerTask] 缓存图片消息: 511875036
2025-07-31 07:36:51 | DEBUG | 收到消息: {'MsgId': 324248353, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_0o755tqnn7p22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>当前微信版本不支持展示该内容，请升级至最新版本。</title>\n\t\t<type>51</type>\n\t\t<url>https://support.weixin.qq.com/update/</url>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<finderFeed>\n\t\t\t<objectId><![CDATA[14712525365767772258]]></objectId>\n\t\t\t<feedType><![CDATA[4]]></feedType>\n\t\t\t<nickname><![CDATA[唱舞全明星]]></nickname>\n\t\t\t<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/N9JicFTib4GicYSkqMjCQB9xIGvia21adibACYG6kdicbadDsLJ6yRH5xDncVNfSjoVCOuDIGK7K7J9ucUrNLlXTcVlTyUIMPDUDQPl5uKkzRvmEU/0]]></avatar>\n\t\t\t<desc><![CDATA[【唱舞爆料局】丨小舞不才，吟诗一首\n周年庆，周年庆，周年庆来啦！！\n唱宝们快来看看周年庆的最新爆料吧！\n#唱舞全明星#唱舞全明星手游#taptap#唱舞爆料局#唱舞全明星六周年\n]]></desc>\n\t\t\t<mediaCount><![CDATA[1]]></mediaCount>\n\t\t\t<objectNonceId><![CDATA[12647976122746098036_0_25_62_26_1753918501729812_d0114860-6d9d-11f0-a28c-557b85288916]]></objectNonceId>\n\t\t\t<liveId><![CDATA[0]]></liveId>\n\t\t\t<username><![CDATA[v2_060000231003b20faec8c6ea8b18c6d2cd0de837b07711743f5d7327222b5c3e0a226f07605a@finder]]></username>\n\t\t\t<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/icons_filled_channels_authentication_enterprise_a2658032368245639e666fb11533a600.png]]></authIconUrl>\n\t\t\t<authIconType>2</authIconType>\n\t\t\t<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>\n\t\t\t<sourceCommentScene>25</sourceCommentScene>\n\t\t\t<mediaList>\n\t\t\t\t<media>\n\t\t\t\t\t<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzgPcCw5ewjR2vMCXBEk6sjaticv10sMFNMkzmPEFqWH1KMGZgot1c0AmcxJJmbbNJwfcNB17ibabDU9Mdd9zBCiacg&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=oA9SZ4icv8IuuJsOlibpIBg9wiaJ4ibwFRiaUoaa31INcaJs1a5lfm7Y6J52ELEha6m6icgo6PICynXRiaePDkh27djFguCLENlI2HdFf2bhKiaib0iaggHs08U18oJpkOArP1oqILlRY6Ij4k9LKKjrOQDsfDGA&ctsc=2-25]]></thumbUrl>\n\t\t\t\t\t<fullCoverUrl><![CDATA[]]></fullCoverUrl>\n\t\t\t\t\t<videoPlayDuration><![CDATA[54]]></videoPlayDuration>\n\t\t\t\t\t<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQDyFyiaHBZ6bMDibPlzWldrx91bWUPnKQpw4a3SZuDbonnz8AUnrBUPcXuoR5XibzLt9eh2haqWewzFkfO185QnjR7&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a15c]]></url>\n\t\t\t\t\t<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzgPcCw5ewjR2vMCXBEk6sjaticv10sMFNMkzmPEFqWH1KMGZgot1c0AmcxJJmbbNJwfcNB17ibabDU9Mdd9zBCiacg&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=oA9SZ4icv8IuuJsOlibpIBg9wiaJ4ibwFRiaUoaa31INcaJs1a5lfm7Y6J52ELEha6m6icgo6PICynXRiaePDkh27djFguCLENlI2HdFf2bhKiaib0iaggHs08U18oJpkOArP1oqILlRY6Ij4k9LKKjrOQDsfDGA&ctsc=2-25]]></coverUrl>\n\t\t\t\t\t<height><![CDATA[1080]]></height>\n\t\t\t\t\t<mediaType><![CDATA[4]]></mediaType>\n\t\t\t\t\t<fullClipInset><![CDATA[]]></fullClipInset>\n\t\t\t\t\t<width><![CDATA[1920]]></width>\n\t\t\t\t</media>\n\t\t\t</mediaList>\n\t\t\t<megaVideo>\n\t\t\t\t<objectId><![CDATA[]]></objectId>\n\t\t\t\t<objectNonceId><![CDATA[]]></objectNonceId>\n\t\t\t</megaVideo>\n\t\t\t<bizUsername><![CDATA[]]></bizUsername>\n\t\t\t<bizNickname><![CDATA[唱舞全明星]]></bizNickname>\n\t\t\t<bizAvatar><![CDATA[https://wx.qlogo.cn/mmhead/ver_1/N9JicFTib4GicYSkqMjCQB9xJLPrGQUHyBruHVuEOnIu8eIqtbichUotpRAlArW8TKvVUlN9HpLURlEk02AIwBBnUVpuVCF5UQd2icGkTzzKYONU/132]]></bizAvatar>\n\t\t\t<bizUsernameV2><![CDATA[gh_7057516c9e71]]></bizUsernameV2>\n\t\t\t<bizAuthIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/icons_filled_channels_authentication_enterprise_a2658032368245639e666fb11533a600.png]]></bizAuthIconUrl>\n\t\t\t<bizAuthIconType>2</bizAuthIconType>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<shareBypData><![CDATA[]]></shareBypData>\n\t\t\t<isDebug>0</isDebug>\n\t\t\t<content_type>0</content_type>\n\t\t\t<finderShareExtInfo><![CDATA[{"hasInput":false,"contextId":"26-2-25-fa80ecbd708b7d5265abd9efda314a7b1753918501335","shareSrcScene":4}]]></finderShareExtInfo>\n\t\t\t<finderForwardSource><![CDATA[]]></finderForwardSource>\n\t\t</finderFeed>\n\t</appmsg>\n\t<fromusername>wxid_0o755tqnn7p22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753918613, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>48a1a310dc30986c119954014e319a86_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_62Q/FOaV|v1_4/X9I/SG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1863101559972313168, 'MsgSeq': 871413174}
2025-07-31 07:36:51 | DEBUG | 从群聊消息中提取发送者: wxid_0o755tqnn7p22
2025-07-31 07:36:51 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前微信版本不支持展示该内容，请升级至最新版本。</title>
		<type>51</type>
		<url>https://support.weixin.qq.com/update/</url>
		<appattach>
			<cdnthumbaeskey />
			<aeskey></aeskey>
		</appattach>
		<finderFeed>
			<objectId><![CDATA[14712525365767772258]]></objectId>
			<feedType><![CDATA[4]]></feedType>
			<nickname><![CDATA[唱舞全明星]]></nickname>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/N9JicFTib4GicYSkqMjCQB9xIGvia21adibACYG6kdicbadDsLJ6yRH5xDncVNfSjoVCOuDIGK7K7J9ucUrNLlXTcVlTyUIMPDUDQPl5uKkzRvmEU/0]]></avatar>
			<desc><![CDATA[【唱舞爆料局】丨小舞不才，吟诗一首
周年庆，周年庆，周年庆来啦！！
唱宝们快来看看周年庆的最新爆料吧！
#唱舞全明星#唱舞全明星手游#taptap#唱舞爆料局#唱舞全明星六周年
]]></desc>
			<mediaCount><![CDATA[1]]></mediaCount>
			<objectNonceId><![CDATA[12647976122746098036_0_25_62_26_1753918501729812_d0114860-6d9d-11f0-a28c-557b85288916]]></objectNonceId>
			<liveId><![CDATA[0]]></liveId>
			<username><![CDATA[v2_060000231003b20faec8c6ea8b18c6d2cd0de837b07711743f5d7327222b5c3e0a226f07605a@finder]]></username>
			<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/icons_filled_channels_authentication_enterprise_a2658032368245639e666fb11533a600.png]]></authIconUrl>
			<authIconType>2</authIconType>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<sourceCommentScene>25</sourceCommentScene>
			<mediaList>
				<media>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzgPcCw5ewjR2vMCXBEk6sjaticv10sMFNMkzmPEFqWH1KMGZgot1c0AmcxJJmbbNJwfcNB17ibabDU9Mdd9zBCiacg&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=oA9SZ4icv8IuuJsOlibpIBg9wiaJ4ibwFRiaUoaa31INcaJs1a5lfm7Y6J52ELEha6m6icgo6PICynXRiaePDkh27djFguCLENlI2HdFf2bhKiaib0iaggHs08U18oJpkOArP1oqILlRY6Ij4k9LKKjrOQDsfDGA&ctsc=2-25]]></thumbUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<videoPlayDuration><![CDATA[54]]></videoPlayDuration>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQDyFyiaHBZ6bMDibPlzWldrx91bWUPnKQpw4a3SZuDbonnz8AUnrBUPcXuoR5XibzLt9eh2haqWewzFkfO185QnjR7&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a15c]]></url>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzgPcCw5ewjR2vMCXBEk6sjaticv10sMFNMkzmPEFqWH1KMGZgot1c0AmcxJJmbbNJwfcNB17ibabDU9Mdd9zBCiacg&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=oA9SZ4icv8IuuJsOlibpIBg9wiaJ4ibwFRiaUoaa31INcaJs1a5lfm7Y6J52ELEha6m6icgo6PICynXRiaePDkh27djFguCLENlI2HdFf2bhKiaib0iaggHs08U18oJpkOArP1oqILlRY6Ij4k9LKKjrOQDsfDGA&ctsc=2-25]]></coverUrl>
					<height><![CDATA[1080]]></height>
					<mediaType><![CDATA[4]]></mediaType>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width><![CDATA[1920]]></width>
				</media>
			</mediaList>
			<megaVideo>
				<objectId><![CDATA[]]></objectId>
				<objectNonceId><![CDATA[]]></objectNonceId>
			</megaVideo>
			<bizUsername><![CDATA[]]></bizUsername>
			<bizNickname><![CDATA[唱舞全明星]]></bizNickname>
			<bizAvatar><![CDATA[https://wx.qlogo.cn/mmhead/ver_1/N9JicFTib4GicYSkqMjCQB9xJLPrGQUHyBruHVuEOnIu8eIqtbichUotpRAlArW8TKvVUlN9HpLURlEk02AIwBBnUVpuVCF5UQd2icGkTzzKYONU/132]]></bizAvatar>
			<bizUsernameV2><![CDATA[gh_7057516c9e71]]></bizUsernameV2>
			<bizAuthIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/icons_filled_channels_authentication_enterprise_a2658032368245639e666fb11533a600.png]]></bizAuthIconUrl>
			<bizAuthIconType>2</bizAuthIconType>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<shareBypData><![CDATA[]]></shareBypData>
			<isDebug>0</isDebug>
			<content_type>0</content_type>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"contextId":"26-2-25-fa80ecbd708b7d5265abd9efda314a7b1753918501335","shareSrcScene":4}]]></finderShareExtInfo>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
		</finderFeed>
	</appmsg>
	<fromusername>wxid_0o755tqnn7p22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-31 07:36:51 | DEBUG | XML消息类型: 51
2025-07-31 07:36:51 | DEBUG | XML消息标题: 当前微信版本不支持展示该内容，请升级至最新版本。
2025-07-31 07:36:51 | DEBUG | XML消息URL: https://support.weixin.qq.com/update/
2025-07-31 07:36:51 | INFO | 未知的XML消息类型: 51
2025-07-31 07:36:51 | INFO | 消息标题: 当前微信版本不支持展示该内容，请升级至最新版本。
2025-07-31 07:36:51 | INFO | 消息描述: N/A
2025-07-31 07:36:51 | INFO | 消息URL: https://support.weixin.qq.com/update/
2025-07-31 07:36:51 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前微信版本不支持展示该内容，请升级至最新版本。</title>
		<type>51</type>
		<url>https://support.weixin.qq.com/update/</url>
		<appattach>
			<cdnthumbaeskey />
			<aeskey></aeskey>
		</appattach>
		<finderFeed>
			<objectId><![CDATA[14712525365767772258]]></objectId>
			<feedType><![CDATA[4]]></feedType>
			<nickname><![CDATA[唱舞全明星]]></nickname>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/N9JicFTib4GicYSkqMjCQB9xIGvia21adibACYG6kdicbadDsLJ6yRH5xDncVNfSjoVCOuDIGK7K7J9ucUrNLlXTcVlTyUIMPDUDQPl5uKkzRvmEU/0]]></avatar>
			<desc><![CDATA[【唱舞爆料局】丨小舞不才，吟诗一首
周年庆，周年庆，周年庆来啦！！
唱宝们快来看看周年庆的最新爆料吧！
#唱舞全明星#唱舞全明星手游#taptap#唱舞爆料局#唱舞全明星六周年
]]></desc>
			<mediaCount><![CDATA[1]]></mediaCount>
			<objectNonceId><![CDATA[12647976122746098036_0_25_62_26_1753918501729812_d0114860-6d9d-11f0-a28c-557b85288916]]></objectNonceId>
			<liveId><![CDATA[0]]></liveId>
			<username><![CDATA[v2_060000231003b20faec8c6ea8b18c6d2cd0de837b07711743f5d7327222b5c3e0a226f07605a@finder]]></username>
			<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/icons_filled_channels_authentication_enterprise_a2658032368245639e666fb11533a600.png]]></authIconUrl>
			<authIconType>2</authIconType>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<sourceCommentScene>25</sourceCommentScene>
			<mediaList>
				<media>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzgPcCw5ewjR2vMCXBEk6sjaticv10sMFNMkzmPEFqWH1KMGZgot1c0AmcxJJmbbNJwfcNB17ibabDU9Mdd9zBCiacg&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=oA9SZ4icv8IuuJsOlibpIBg9wiaJ4ibwFRiaUoaa31INcaJs1a5lfm7Y6J52ELEha6m6icgo6PICynXRiaePDkh27djFguCLENlI2HdFf2bhKiaib0iaggHs08U18oJpkOArP1oqILlRY6Ij4k9LKKjrOQDsfDGA&ctsc=2-25]]></thumbUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<videoPlayDuration><![CDATA[54]]></videoPlayDuration>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQDyFyiaHBZ6bMDibPlzWldrx91bWUPnKQpw4a3SZuDbonnz8AUnrBUPcXuoR5XibzLt9eh2haqWewzFkfO185QnjR7&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a15c]]></url>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzgPcCw5ewjR2vMCXBEk6sjaticv10sMFNMkzmPEFqWH1KMGZgot1c0AmcxJJmbbNJwfcNB17ibabDU9Mdd9zBCiacg&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=oA9SZ4icv8IuuJsOlibpIBg9wiaJ4ibwFRiaUoaa31INcaJs1a5lfm7Y6J52ELEha6m6icgo6PICynXRiaePDkh27djFguCLENlI2HdFf2bhKiaib0iaggHs08U18oJpkOArP1oqILlRY6Ij4k9LKKjrOQDsfDGA&ctsc=2-25]]></coverUrl>
					<height><![CDATA[1080]]></height>
					<mediaType><![CDATA[4]]></mediaType>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width><![CDATA[1920]]></width>
				</media>
			</mediaList>
			<megaVideo>
				<objectId><![CDATA[]]></objectId>
				<objectNonceId><![CDATA[]]></objectNonceId>
			</megaVideo>
			<bizUsername><![CDATA[]]></bizUsername>
			<bizNickname><![CDATA[唱舞全明星]]></bizNickname>
			<bizAvatar><![CDATA[https://wx.qlogo.cn/mmhead/ver_1/N9JicFTib4GicYSkqMjCQB9xJLPrGQUHyBruHVuEOnIu8eIqtbichUotpRAlArW8TKvVUlN9HpLURlEk02AIwBBnUVpuVCF5UQd2icGkTzzKYONU/132]]></bizAvatar>
			<bizUsernameV2><![CDATA[gh_7057516c9e71]]></bizUsernameV2>
			<bizAuthIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/icons_filled_channels_authentication_enterprise_a2658032368245639e666fb11533a600.png]]></bizAuthIconUrl>
			<bizAuthIconType>2</bizAuthIconType>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<shareBypData><![CDATA[]]></shareBypData>
			<isDebug>0</isDebug>
			<content_type>0</content_type>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"contextId":"26-2-25-fa80ecbd708b7d5265abd9efda314a7b1753918501335","shareSrcScene":4}]]></finderShareExtInfo>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
		</finderFeed>
	</appmsg>
	<fromusername>wxid_0o755tqnn7p22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-31 07:37:40 | DEBUG | 收到消息: {'MsgId': 1199854712, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_0o755tqnn7p22:\n舞团排行榜？？？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753918661, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_pNW+WetF|v1_9TsjCI0T</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1018310007554564956, 'MsgSeq': 871413175}
2025-07-31 07:37:40 | INFO | 收到文本消息: 消息ID:1199854712 来自:27852221909@chatroom 发送人:wxid_0o755tqnn7p22 @:[] 内容:舞团排行榜？？？
2025-07-31 07:37:40 | DEBUG | 处理消息内容: '舞团排行榜？？？'
2025-07-31 07:37:40 | DEBUG | 消息内容 '舞团排行榜？？？' 不匹配任何命令，忽略
2025-07-31 07:42:50 | DEBUG | 收到消息: {'MsgId': 1805552484, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n游戏怎么又下载资源'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753918972, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_KRyZUIAy|v1_dS3yByLr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2740776984221962745, 'MsgSeq': 871413176}
2025-07-31 07:42:50 | INFO | 收到文本消息: 消息ID:1805552484 来自:27852221909@chatroom 发送人:wxid_ctp9qffuf14b21 @:[] 内容:游戏怎么又下载资源
2025-07-31 07:42:50 | DEBUG | 处理消息内容: '游戏怎么又下载资源'
2025-07-31 07:42:50 | DEBUG | 消息内容 '游戏怎么又下载资源' 不匹配任何命令，忽略
2025-07-31 07:43:00 | DEBUG | 收到消息: {'MsgId': 1045495185, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n团房没掉吧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753918982, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_/owbuvnY|v1_ulSEBgNE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8146794487585615395, 'MsgSeq': 871413177}
2025-07-31 07:43:00 | INFO | 收到文本消息: 消息ID:1045495185 来自:27852221909@chatroom 发送人:wxid_ctp9qffuf14b21 @:[] 内容:团房没掉吧
2025-07-31 07:43:00 | DEBUG | 处理消息内容: '团房没掉吧'
2025-07-31 07:43:00 | DEBUG | 消息内容 '团房没掉吧' 不匹配任何命令，忽略
2025-07-31 07:56:18 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-31 07:59:49 | DEBUG | 收到消息: {'MsgId': 108815237, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n维护了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753919991, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_LeD/il6A|v1_LuPIr/ny</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4811268670743861961, 'MsgSeq': 871413178}
2025-07-31 07:59:49 | INFO | 收到文本消息: 消息ID:108815237 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:维护了
2025-07-31 07:59:49 | DEBUG | 处理消息内容: '维护了'
2025-07-31 07:59:49 | DEBUG | 消息内容 '维护了' 不匹配任何命令，忽略
2025-07-31 07:59:55 | DEBUG | 收到消息: {'MsgId': 767184654, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n怎么不掉'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753919997, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_OpFQlSEO|v1_Lqz3iHU7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2118789687501815690, 'MsgSeq': 871413179}
2025-07-31 07:59:55 | INFO | 收到文本消息: 消息ID:767184654 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:怎么不掉
2025-07-31 07:59:55 | DEBUG | 处理消息内容: '怎么不掉'
2025-07-31 07:59:55 | DEBUG | 消息内容 '怎么不掉' 不匹配任何命令，忽略
2025-07-31 07:59:58 | DEBUG | 收到消息: {'MsgId': 1972816358, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_2530z9t0joek22:\n<msg><emoji fromusername = "wxid_2530z9t0joek22" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="228d134484fcf4cc1074a1492c72691b" len = "1497794" productid="" androidmd5="228d134484fcf4cc1074a1492c72691b" androidlen="1497794" s60v3md5 = "228d134484fcf4cc1074a1492c72691b" s60v3len="1497794" s60v5md5 = "228d134484fcf4cc1074a1492c72691b" s60v5len="1497794" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=228d134484fcf4cc1074a1492c72691b&amp;filekey=30440201010430302e02016e0402534804203232386431333434383466636634636331303734613134393263373236393162020316dac2040d00000004627466730000000132&amp;hy=SH&amp;storeid=2680eff2d000c87f90a0d63c80000006e01004fb1534819f3b031568bc3116&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=75fc423fbd91e4190dc13192b07b04de&amp;filekey=30440201010430302e02016e0402534804203735666334323366626439316534313930646331333139326230376230346465020316dad0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2680eff2d000ed2540a0d63c80000006e02004fb2534819f3b031568bc313f&amp;ef=2&amp;bizid=1022" aeskey= "970a1f92107742bfa6a00004214eb722" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=29c945f9dbdc1c7ea317b80533b22e40&amp;filekey=30440201010430302e02016e04025348042032396339343566396462646331633765613331376238303533336232326534300203030700040d00000004627466730000000132&amp;hy=SH&amp;storeid=2680eff2e0001b70c0a0d63c80000006e03004fb3534819f3b031568bc315d&amp;ef=3&amp;bizid=1022" externmd5 = "c1e81be394c0cc15b6190a87475031fb" width= "225" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753919999, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_C283V5Gg|v1_/7oxuaSV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6805521678311666746, 'MsgSeq': 871413180}
2025-07-31 07:59:58 | INFO | 收到表情消息: 消息ID:1972816358 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 MD5:228d134484fcf4cc1074a1492c72691b 大小:1497794
2025-07-31 07:59:58 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6805521678311666746
2025-07-31 08:00:00 | INFO | [QuarkSignIn] 开始执行定时自动签到任务
2025-07-31 08:00:00 | DEBUG | [QuarkSignIn] API响应状态码: 200
2025-07-31 08:00:02 | DEBUG | [QuarkSignIn] 签到响应状态码: 200
2025-07-31 08:00:02 | INFO | 发送文字消息: 对方wxid:wxid_ubbh6q832tcs21 at: 内容:🤖 夸克网盘自动签到完成

🌟 夸克网盘签到开始
📊 检测到 1 个账号

🔄 第1个账号签到中...
👤 普通用户 夸父4527
💾 网盘总容量：19.93 GB
📈 签到累计容量：5.93 GB
🎉 今日签到成功+20.00 MB，连签进度(1/7)

✨ 夸克网盘签到完成
2025-07-31 08:00:02 | INFO | [QuarkSignIn] 已发送签到通知到: wxid_ubbh6q832tcs21
2025-07-31 08:08:53 | DEBUG | 收到消息: {'MsgId': 2040034303, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_besewpsontwy29:\n签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753920534, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_SLDr0/Jp|v1_GY6BsVVE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2462355751170518392, 'MsgSeq': 871413183}
2025-07-31 08:08:53 | INFO | 收到文本消息: 消息ID:2040034303 来自:27852221909@chatroom 发送人:wxid_besewpsontwy29 @:[] 内容:签到
2025-07-31 08:08:53 | DEBUG | 处理消息内容: '签到'
2025-07-31 08:08:53 | DEBUG | 消息内容 '签到' 不匹配任何命令，忽略
2025-07-31 08:08:53 | INFO | 数据库: 用户wxid_besewpsontwy29登录时间设置为2025-07-31 00:00:00+08:00
2025-07-31 08:08:53 | INFO | 数据库: 用户wxid_besewpsontwy29连续签到天数设置为10
2025-07-31 08:08:53 | INFO | 数据库: 用户wxid_besewpsontwy29积分增加8
2025-07-31 08:08:54 | INFO | 发送文字消息: 对方wxid:27852221909@chatroom at:['wxid_besewpsontwy29'] 内容:@穆穆 
-----XYBot-----
签到成功！你领到了 6 个积分！✅
你是今天第 1 个签到的！🎉
你连续签到了 10 天！ 再奖励 2 积分！[爱心]
2025-07-31 08:11:14 | DEBUG | 收到消息: {'MsgId': 1902961091, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'qianting1731076232:\n签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753920676, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_Bby9e/id|v1_2W0PKUUz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5785249494444736237, 'MsgSeq': 871413186}
2025-07-31 08:11:14 | INFO | 收到文本消息: 消息ID:1902961091 来自:27852221909@chatroom 发送人:qianting1731076232 @:[] 内容:签到
2025-07-31 08:11:14 | DEBUG | 处理消息内容: '签到'
2025-07-31 08:11:14 | DEBUG | 消息内容 '签到' 不匹配任何命令，忽略
2025-07-31 08:11:14 | INFO | 数据库: 用户qianting1731076232登录时间设置为2025-07-31 00:00:00+08:00
2025-07-31 08:11:14 | INFO | 数据库: 用户qianting1731076232连续签到天数设置为8
2025-07-31 08:11:14 | INFO | 数据库: 用户qianting1731076232积分增加5
2025-07-31 08:11:15 | INFO | 发送文字消息: 对方wxid:27852221909@chatroom at:['qianting1731076232'] 内容:@奈斯༩༧ 
-----XYBot-----
签到成功！你领到了 4 个积分！✅
你是今天第 2 个签到的！🎉
你连续签到了 8 天！ 再奖励 1 积分！[爱心]
2025-07-31 08:23:44 | DEBUG | 收到消息: {'MsgId': 1857804602, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n星期四了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753921426, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_rcK2oc7S|v1_qGebUENs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 星期四了', 'NewMsgId': 4264336426192604698, 'MsgSeq': 871413189}
2025-07-31 08:23:44 | INFO | 收到文本消息: 消息ID:1857804602 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:星期四了
2025-07-31 08:23:45 | DEBUG | 处理消息内容: '星期四了'
2025-07-31 08:23:45 | DEBUG | 消息内容 '星期四了' 不匹配任何命令，忽略
2025-07-31 08:23:52 | DEBUG | 收到消息: {'MsgId': 2047727621, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="1" idbuffer="media:0_0" md5="4f1d9c19a56345b721c80fc5dc904128" len = "54235" productid="" androidmd5="4f1d9c19a56345b721c80fc5dc904128" androidlen="54235" s60v3md5 = "4f1d9c19a56345b721c80fc5dc904128" s60v3len="54235" s60v5md5 = "4f1d9c19a56345b721c80fc5dc904128" s60v5len="54235" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=4f1d9c19a56345b721c80fc5dc904128&amp;filekey=30440201010430302e02016e0402534804203466316439633139613536333435623732316338306663356463393034313238020300d3db040d00000004627466730000000132&amp;hy=SH&amp;storeid=26676f0fb0001e5147b589c7a0000006e01004fb1534823783bc1e66b06bdd&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=e7507e45bdd6b0f2569ad88d5195a338&amp;filekey=30440201010430302e02016e0402534804206537353037653435626464366230663235363961643838643531393561333338020300d3e0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26676f0fb00028dc97b589c7a0000006e02004fb2534823783bc1e66b06be4&amp;ef=2&amp;bizid=1022" aeskey= "a5bde6462cd44a94b3aebcc1e7abc257" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=408d6bc77cd4981fa0ba8b1c3fafe3de&amp;filekey=3043020101042f302d02016e040253480420343038643662633737636434393831666130626138623163336661666533646502022890040d00000004627466730000000132&amp;hy=SH&amp;storeid=26676f0fb000330987b589c7a0000006e03004fb3534823783bc1e66b06bf0&amp;ef=3&amp;bizid=1022" externmd5 = "804b5f64251b6f1baaf25b857664d16a" width= "640" height= "629" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753921434, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_m0INk1ws|v1_PZAWfXJa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一个表情', 'NewMsgId': 5723171982508661962, 'MsgSeq': 871413190}
2025-07-31 08:23:52 | INFO | 收到表情消息: 消息ID:2047727621 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:4f1d9c19a56345b721c80fc5dc904128 大小:54235
2025-07-31 08:23:53 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5723171982508661962
2025-07-31 08:23:56 | DEBUG | 收到消息: {'MsgId': 186996297, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n起床'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753921438, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ujJsf3k1|v1_jW29bWva</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 起床', 'NewMsgId': 7858441512435870233, 'MsgSeq': 871413191}
2025-07-31 08:23:56 | INFO | 收到文本消息: 消息ID:186996297 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:起床
2025-07-31 08:23:57 | DEBUG | 处理消息内容: '起床'
2025-07-31 08:23:57 | DEBUG | 消息内容 '起床' 不匹配任何命令，忽略
2025-07-31 08:24:18 | DEBUG | 收到消息: {'MsgId': 2027465187, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="1" idbuffer="media:0_0" md5="7ed65ed9923bf30c677db00baeb37599" len = "86772" productid="com.tencent.xin.emoticon.person.stiker_1460436893ee99f356b93cba54" androidmd5="7ed65ed9923bf30c677db00baeb37599" androidlen="86772" s60v3md5 = "7ed65ed9923bf30c677db00baeb37599" s60v3len="86772" s60v5md5 = "7ed65ed9923bf30c677db00baeb37599" s60v5len="86772" cdnurl = "http://mmbiz.qpic.cn/mmemoticon/ajNVdqHZLLDOlsGtttOfyHaH8oicBtImQbHERLgTHIicLz3iayFeqprfyMeacRPvy3U/0" designerid = "" thumburl = "http://mmbiz.qpic.cn/mmemoticon/ajNVdqHZLLDOlsGtttOfyMKjKtM3cQeuGUk7uib48jicnjicM0IqwI7Hbwvia9Oax5KR/0" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=3065962c464cb252844f140c020e432e&amp;filekey=30350201010421301f020201060402534804103065962c464cb252844f140c020e432e0203015300040d00000004627466730000000132&amp;hy=SH&amp;storeid=26316b25e0006f6a4000000000000010600004f50534825bd2a00b78147002&amp;bizid=1023" aeskey= "573da4db08a327daf24619aee0e33625" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=1ac0a9517e676823a2808b2fa1688d3c&amp;filekey=30340201010420301e020201060402534804101ac0a9517e676823a2808b2fa1688d3c02027200040d00000004627466730000000132&amp;hy=SH&amp;storeid=26316b25e000971bc000000000000010600004f5053481d167b40b78180128&amp;bizid=1023" externmd5 = "c2fb97e09405261cb494f89ddf6af2b0" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "Cg8KBXpoX2NuEgbotbfluooKCQoFemhfdHcSAAoLCgdkZWZhdWx0EgA=" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753921460, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_B2bblP+N|v1_C+XJN+7B</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一个表情', 'NewMsgId': 1501087714566430413, 'MsgSeq': 871413192}
2025-07-31 08:24:18 | INFO | 收到表情消息: 消息ID:2027465187 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:7ed65ed9923bf30c677db00baeb37599 大小:86772
2025-07-31 08:24:18 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1501087714566430413
2025-07-31 08:24:25 | DEBUG | 收到消息: {'MsgId': 23723502, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n洞房'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753921467, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_s25Ll37l|v1_hGkwV6Zw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 洞房', 'NewMsgId': 5289021404159954086, 'MsgSeq': 871413193}
2025-07-31 08:24:25 | INFO | 收到文本消息: 消息ID:23723502 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:洞房
2025-07-31 08:24:26 | DEBUG | 处理消息内容: '洞房'
2025-07-31 08:24:26 | DEBUG | 消息内容 '洞房' 不匹配任何命令，忽略
2025-07-31 08:24:28 | DEBUG | 收到消息: {'MsgId': 1026998296, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n💕 [小爱]👩\u200d❤\u200d👨[锦岚]💕\n 💓═☘︎═•洞💗房•═☘︎═💓\n👩\u200d❤\u200d👨关系：情侣\n⛺地点：洗手间\n😍活动：擎天柱兼听\n😘结果：成功\n🤱状态：火山曝发般飞渐\n💓═☘︎══•💗•══☘︎═💓\n🔮魅力: +24\n[玫瑰]恩爱: +1\n🕒下次:2025-07-31 08:44:29'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753921468, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_tsMpJkK6|v1_gAjXygUV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 : \ue327 [小爱]\ue005\u200d\ue022\u200d\ue004[锦岚]\ue327\n \ue327═☘︎═•洞\ue328房•═☘︎═...', 'NewMsgId': 4465277249113174098, 'MsgSeq': 871413194}
2025-07-31 08:24:28 | INFO | 收到文本消息: 消息ID:1026998296 来自:48097389945@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容:💕 [小爱]👩‍❤‍👨[锦岚]💕
 💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：洗手间
😍活动：擎天柱兼听
😘结果：成功
🤱状态：火山曝发般飞渐
💓═☘︎══•💗•══☘︎═💓
🔮魅力: +24
[玫瑰]恩爱: +1
🕒下次:2025-07-31 08:44:29
2025-07-31 08:24:28 | DEBUG | 处理消息内容: '💕 [小爱]👩‍❤‍👨[锦岚]💕
 💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：洗手间
😍活动：擎天柱兼听
😘结果：成功
🤱状态：火山曝发般飞渐
💓═☘︎══•💗•══☘︎═💓
🔮魅力: +24
[玫瑰]恩爱: +1
🕒下次:2025-07-31 08:44:29'
2025-07-31 08:24:28 | DEBUG | 消息内容 '💕 [小爱]👩‍❤‍👨[锦岚]💕
 💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：洗手间
😍活动：擎天柱兼听
😘结果：成功
🤱状态：火山曝发般飞渐
💓═☘︎══•💗•══☘︎═💓
🔮魅力: +24
[玫瑰]恩爱: +1
🕒下次:2025-07-31 08:44:29' 不匹配任何命令，忽略
2025-07-31 08:24:31 | DEBUG | 收到消息: {'MsgId': 1899297169, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n「小爱」[爱心]「锦岚」\n地点：小汽车\n活动：双修\n结果：失败\n羞羞：声音太大引起交警注意\n恩爱值减少500\n\n下次:2025-07-31 08:34:27'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753921468, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_emafXOtN|v1_UpEp65my</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110 : 「小爱」[爱心]「锦岚」\n地点：小汽车\n活动：双修\n结果：失败\n羞羞...', 'NewMsgId': 6541450915080800083, 'MsgSeq': 871413195}
2025-07-31 08:24:31 | INFO | 收到文本消息: 消息ID:1899297169 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 @:[] 内容:「小爱」[爱心]「锦岚」
地点：小汽车
活动：双修
结果：失败
羞羞：声音太大引起交警注意
恩爱值减少500

下次:2025-07-31 08:34:27
2025-07-31 08:24:31 | DEBUG | 处理消息内容: '「小爱」[爱心]「锦岚」
地点：小汽车
活动：双修
结果：失败
羞羞：声音太大引起交警注意
恩爱值减少500

下次:2025-07-31 08:34:27'
2025-07-31 08:24:31 | DEBUG | 消息内容 '「小爱」[爱心]「锦岚」
地点：小汽车
活动：双修
结果：失败
羞羞：声音太大引起交警注意
恩爱值减少500

下次:2025-07-31 08:34:27' 不匹配任何命令，忽略
2025-07-31 08:24:33 | DEBUG | 收到消息: {'MsgId': 340692494, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'xiaomaochong:\n<msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="1" idbuffer="media:0_0" md5="9ad9bee59973656c2a2834e4105aa6ff" len="571349" productid="" androidmd5="9ad9bee59973656c2a2834e4105aa6ff" androidlen="571349" s60v3md5="9ad9bee59973656c2a2834e4105aa6ff" s60v3len="571349" s60v5md5="9ad9bee59973656c2a2834e4105aa6ff" s60v5len="571349" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=9ad9bee59973656c2a2834e4105aa6ff&amp;filekey=30440201010430302e02016e0402535a04203961643962656535393937333635366332613238333465343130356161366666020308b7d5040d00000004627466730000000132&amp;hy=SZ&amp;storeid=268673fde000c554c70cc3bc90000006e01004fb1535a0164b54157ade9ae2&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=00bf4ca74ec6af527e2ecdac9e6146d8&amp;filekey=30440201010430302e02016e0402535a04203030626634636137346563366166353237653265636461633965363134366438020308b7e0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=268673fde000e0f1570cc3bc90000006e02004fb2535a0164b54157ade9b00&amp;ef=2&amp;bizid=1022" aeskey="4c2595910669455cb9c7d54b56fff619" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=34f24924ec5b9ff3d7c1ecc8da78e5d7&amp;filekey=30440201010430302e02016e0402535a04203334663234393234656335623966663364376331656363386461373865356437020300f080040d00000004627466730000000132&amp;hy=SZ&amp;storeid=268673fdf0000446670cc3bc90000006e03004fb3535a0164b54157ade9b23&amp;ef=3&amp;bizid=1022" externmd5="d9cb4c8236726054b53c490cd6c0416f" width="640" height="640" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753921473, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_JF6C69hv|v1_GWLFZPmf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一个表情', 'NewMsgId': 5541123780615731799, 'MsgSeq': 871413196}
2025-07-31 08:24:33 | INFO | 收到表情消息: 消息ID:340692494 来自:48097389945@chatroom 发送人:xiaomaochong MD5:9ad9bee59973656c2a2834e4105aa6ff 大小:571349
2025-07-31 08:24:34 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5541123780615731799
2025-07-31 08:24:56 | DEBUG | 收到消息: {'MsgId': 876090863, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="59999" length="630292" bufid="0" aeskey="f82ce55abc40e912d729661c06fc983e" voiceurl="3051020100044a304802010002035a663f02032f514902041d328e710204688ab7d9042462326231306139312d396137372d346234342d613138632d38356261383937353730363402040524000f0201000400" voicemd5="" clientmsgid="41386366366231333863396431366400560824073125d67e8f98550101" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753921497, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_8Rnfid+T|v1_MkL7WeJT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 3093207871191708883, 'MsgSeq': 871413197}
2025-07-31 08:24:56 | INFO | 收到语音消息: 消息ID:876090863 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="59999" length="630292" bufid="0" aeskey="f82ce55abc40e912d729661c06fc983e" voiceurl="3051020100044a304802010002035a663f02032f514902041d328e710204688ab7d9042462326231306139312d396137372d346234342d613138632d38356261383937353730363402040524000f0201000400" voicemd5="" clientmsgid="41386366366231333863396431366400560824073125d67e8f98550101" fromusername="xiaomaochong" /></msg>
2025-07-31 08:25:43 | DEBUG | 收到消息: {'MsgId': 1234068209, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx485a97c844086dc9" sdkver="0">\n\t\t<title>辞九门回忆（钱是英雄胆）（徐潇 Remix）</title>\n\t\t<des>小爱</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>3</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl>https://v26-luna.douyinvod.com/400c0a5a29f75526e7e22146dd35b252/688c182f/video/tos/cn/tos-cn-ve-2774/okygNIOMDEcSlBVfgEfJCPQLNFtDBoAtl9zZMC/</dataurl>\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>\n\t\t<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/6iaSQLKjLK6YMpTGwdaFEJHr0OKpQPK5HR2zUmibE6f8W4tsSjkT8RbjHCBOnoIdKv28tarjHhJic6qRhiczIdOzbEUTMQebeT1XouSEMct9ofiaha74cyibDl8vxtUztxWIicB/0</songalbumurl>\n\t\t<songlyric>[00:00.00]看的全都是那诡谲云涌\n[00:07.31]入得此门不回首\n[00:11.47]无需宣之于口\n[00:14.71]我对案再拜那风雨瓢泼的残陋\n[00:22.12]再聚首\n[00:24.91]戏子多秋\n[00:27.59]可怜一处情深旧\n[00:31.31]满座衣冠皆老朽\n[00:34.99]黄泉故事无止休\n[00:38.71]戏无骨难左右\n[00:42.35]换过一折又重头\n[00:46.07]只道最是人间不能留\n</songlyric>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>29</version>\n\t\t<appname>摇一摇搜歌</appname>\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753921545, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>eca95f323827affff8022767828e1f41_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_FAcEypeK|v1_xNth8ddI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 1606169374064625874, 'MsgSeq': 871413198}
2025-07-31 08:25:43 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-07-31 08:25:43 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wx485a97c844086dc9" sdkver="0">
		<title>辞九门回忆（钱是英雄胆）（徐潇 Remix）</title>
		<des>小爱</des>
		<username />
		<action>view</action>
		<type>3</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl>https://v26-luna.douyinvod.com/400c0a5a29f75526e7e22146dd35b252/688c182f/video/tos/cn/tos-cn-ve-2774/okygNIOMDEcSlBVfgEfJCPQLNFtDBoAtl9zZMC/</dataurl>
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>2</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>
		<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/6iaSQLKjLK6YMpTGwdaFEJHr0OKpQPK5HR2zUmibE6f8W4tsSjkT8RbjHCBOnoIdKv28tarjHhJic6qRhiczIdOzbEUTMQebeT1XouSEMct9ofiaha74cyibDl8vxtUztxWIicB/0</songalbumurl>
		<songlyric>[00:00.00]看的全都是那诡谲云涌
[00:07.31]入得此门不回首
[00:11.47]无需宣之于口
[00:14.71]我对案再拜那风雨瓢泼的残陋
[00:22.12]再聚首
[00:24.91]戏子多秋
[00:27.59]可怜一处情深旧
[00:31.31]满座衣冠皆老朽
[00:34.99]黄泉故事无止休
[00:38.71]戏无骨难左右
[00:42.35]换过一折又重头
[00:46.07]只道最是人间不能留
</songlyric>
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>29</version>
		<appname>摇一摇搜歌</appname>
	</appinfo>
	<commenturl />
</msg>

2025-07-31 08:25:43 | DEBUG | XML消息类型: 3
2025-07-31 08:25:43 | DEBUG | XML消息标题: 辞九门回忆（钱是英雄胆）（徐潇 Remix）
2025-07-31 08:25:43 | DEBUG | XML消息描述: 小爱
2025-07-31 08:25:43 | DEBUG | 附件信息 totallen: 0
2025-07-31 08:25:43 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-31 08:25:43 | INFO | 收到红包消息: 标题:辞九门回忆（钱是英雄胆）（徐潇 Remix） 描述:小爱 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-31 08:26:18 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-31 08:26:19 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-31 08:26:20 | DEBUG | 群成员变化检查完成
2025-07-31 08:29:28 | DEBUG | 收到消息: {'MsgId': 1409069149, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'xiaomaochong:\n<msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="ec659f94db17b05434544a0c377ddfc0" len="394561" productid="" androidmd5="ec659f94db17b05434544a0c377ddfc0" androidlen="394561" s60v3md5="ec659f94db17b05434544a0c377ddfc0" s60v3len="394561" s60v5md5="ec659f94db17b05434544a0c377ddfc0" s60v5len="394561" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=ec659f94db17b05434544a0c377ddfc0&amp;filekey=30440201010430302e02016e0402535a042065633635396639346462313762303534333435343461306333373764646663300203060541040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b0003337bf5dee8850000006e01004fb1535a29cfe011568865606&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=38d4d15c316659b4e890b4e3c7ac3c7c&amp;filekey=30440201010430302e02016e0402535a042033386434643135633331363635396234653839306234653363376163336337630203060550040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b000417bdf5dee8850000006e02004fb2535a29cfe01156886561e&amp;ef=2&amp;bizid=1022" aeskey="695df9c553cb498d96d7ab1f98a32e42" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=7cbc2c2ad453fa44f6919d2c2cee191c&amp;filekey=30440201010430302e02016e0402535a042037636263326332616434353366613434663639313964326332636565313931630203009130040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b0005369ef5dee8850000006e03004fb3535a29cfe011568865632&amp;ef=3&amp;bizid=1022" externmd5="018e81e27adf973e3d91f2286238160a" width="450" height="450" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753921769, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_QdsKKveI|v1_aA4S97ZG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一个表情', 'NewMsgId': 2390666514322243175, 'MsgSeq': 871413199}
2025-07-31 08:29:28 | INFO | 收到表情消息: 消息ID:1409069149 来自:48097389945@chatroom 发送人:xiaomaochong MD5:ec659f94db17b05434544a0c377ddfc0 大小:394561
2025-07-31 08:29:28 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2390666514322243175
2025-07-31 08:30:00 | INFO | [TimerTask] 清理过期图片缓存: 1个
2025-07-31 08:30:19 | DEBUG | 收到消息: {'MsgId': 1199215504, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n兄弟们'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753921821, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ywIE7gpS|v1_cdI4qWpA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 兄弟们', 'NewMsgId': 4868403557610275505, 'MsgSeq': 871413200}
2025-07-31 08:30:19 | INFO | 收到文本消息: 消息ID:1199215504 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:兄弟们
2025-07-31 08:30:20 | DEBUG | 处理消息内容: '兄弟们'
2025-07-31 08:30:20 | DEBUG | 消息内容 '兄弟们' 不匹配任何命令，忽略
2025-07-31 08:30:22 | DEBUG | 收到消息: {'MsgId': 1266789658, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n周四了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753921822, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_O13hFt0w|v1_LDtoyLNX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 周四了', 'NewMsgId': 4134602854820462020, 'MsgSeq': 871413201}
2025-07-31 08:30:22 | INFO | 收到文本消息: 消息ID:1266789658 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:周四了
2025-07-31 08:30:22 | DEBUG | 处理消息内容: '周四了'
2025-07-31 08:30:22 | DEBUG | 消息内容 '周四了' 不匹配任何命令，忽略
2025-07-31 08:30:28 | DEBUG | 收到消息: {'MsgId': 1810422402, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n可以出来吹牛逼了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753921830, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_f10vMBXP|v1_9MVpblqK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 可以出来吹牛逼了', 'NewMsgId': 6048306697568841685, 'MsgSeq': 871413202}
2025-07-31 08:30:28 | INFO | 收到文本消息: 消息ID:1810422402 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:可以出来吹牛逼了
2025-07-31 08:30:29 | DEBUG | 处理消息内容: '可以出来吹牛逼了'
2025-07-31 08:30:29 | DEBUG | 消息内容 '可以出来吹牛逼了' 不匹配任何命令，忽略
2025-07-31 08:35:26 | DEBUG | 收到消息: {'MsgId': 1568546602, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_558jw1jndlum22:\n签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922128, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_nS7Nsk99|v1_94Vih5cj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2161979953401674419, 'MsgSeq': 871413203}
2025-07-31 08:35:26 | INFO | 收到文本消息: 消息ID:1568546602 来自:27852221909@chatroom 发送人:wxid_558jw1jndlum22 @:[] 内容:签到
2025-07-31 08:35:26 | DEBUG | 处理消息内容: '签到'
2025-07-31 08:35:26 | DEBUG | 消息内容 '签到' 不匹配任何命令，忽略
2025-07-31 08:35:26 | INFO | 数据库: 用户wxid_558jw1jndlum22登录时间设置为2025-07-31 00:00:00+08:00
2025-07-31 08:35:26 | INFO | 数据库: 用户wxid_558jw1jndlum22连续签到天数设置为2
2025-07-31 08:35:26 | INFO | 数据库: 用户wxid_558jw1jndlum22积分增加20
2025-07-31 08:35:27 | INFO | 发送文字消息: 对方wxid:27852221909@chatroom at:['wxid_558jw1jndlum22'] 内容:@上上签。 
-----XYBot-----
签到成功！你领到了 20 个积分！✅
你是今天第 3 个签到的！🎉
你连续签到了 2 天！[爱心]
2025-07-31 08:35:32 | DEBUG | 收到消息: {'MsgId': 398195716, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n好'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922134, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_mVPjk7d4|v1_3lB06PTw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 好', 'NewMsgId': 2915784214651204767, 'MsgSeq': 871413206}
2025-07-31 08:35:32 | INFO | 收到文本消息: 消息ID:398195716 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:好
2025-07-31 08:35:32 | DEBUG | 处理消息内容: '好'
2025-07-31 08:35:32 | DEBUG | 消息内容 '好' 不匹配任何命令，忽略
2025-07-31 08:35:53 | DEBUG | 收到消息: {'MsgId': 1606201209, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="e61e743283c2198d1f434f2562aeebab" len="22608" productid="" androidmd5="e61e743283c2198d1f434f2562aeebab" androidlen="22608" s60v3md5="e61e743283c2198d1f434f2562aeebab" s60v3len="22608" s60v5md5="e61e743283c2198d1f434f2562aeebab" s60v5len="22608" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=e61e743283c2198d1f434f2562aeebab&amp;filekey=3043020101042f302d02016e0402535a0420653631653734333238336332313938643166343334663235363261656562616202025850040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2626bd94f000006b2537480a90000006e01004fb1535a29dd6870b644197b8&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=d431f24838d7167df08a711520af0079&amp;filekey=3043020101042f302d02016e0402535a0420643433316632343833386437313637646630386137313135323061663030373902025860040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2626bd94f0000aa17537480a90000006e02004fb2535a29dd6870b644197c4&amp;ef=2&amp;bizid=1022" aeskey="00f007e39e3c4f11aa9b12eaa198e1ce" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=08fcb3d1e28145787ec7061c4bd73476&amp;filekey=3043020101042f302d02016e0402535a04203038666362336431653238313435373837656337303631633462643733343736020234e0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2626bd94f00013941537480a90000006e03004fb3535a29dd6870b644197d3&amp;ef=3&amp;bizid=1022" externmd5="3db589dcbbc43051084c7f698f19b62d" width="440" height="432" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922154, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_2R03WCU0|v1_b3xLLaY5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 719270118494769915, 'MsgSeq': 871413207}
2025-07-31 08:35:53 | INFO | 收到表情消息: 消息ID:1606201209 来自:48097389945@chatroom 发送人:last--exile MD5:e61e743283c2198d1f434f2562aeebab 大小:22608
2025-07-31 08:35:53 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 719270118494769915
2025-07-31 08:37:18 | DEBUG | 收到消息: {'MsgId': 74387264, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'xiaomaochong:\n<msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="949c22ce23000c4fecb52cc8d2101e2d" len="788489" productid="" androidmd5="949c22ce23000c4fecb52cc8d2101e2d" androidlen="788489" s60v3md5="949c22ce23000c4fecb52cc8d2101e2d" s60v3len="788489" s60v5md5="949c22ce23000c4fecb52cc8d2101e2d" s60v5len="788489" cdnurl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=949c22ce23000c4fecb52cc8d2101e2d&amp;filekey=30440201010430302e02016e040253480420393439633232636532333030306334666563623532636338643231303165326402030c0809040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687371c0000b9154252daa890000006e01004fb2534800734031504b444c0&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=b3659f68af3b843fef7b0a3338980464&amp;filekey=30440201010430302e02016e040253480420623336353966363861663362383433666566376230613333333839383034363402030c0810040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687371c0000cd23e252daa890000006e02004fb2534800734031504b444e3&amp;ef=2&amp;bizid=1022" aeskey="4d39df5328cc4fe58abb05f8bc80f02c" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=265d6f53c33fffff83ba067fe19778a9&amp;filekey=3043020101042f302d02016e0402534804203236356436663533633333666666666638336261303637666531393737386139020248e0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687371c0000f3d19252daa890000006e03004fb3534800734031504b4450d&amp;ef=3&amp;bizid=1022" externmd5="80b2f6ecfc78a63adbf5693cc4184f89" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': **********, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_qnPiLRw7|v1_G11Vy7aE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一个表情', 'NewMsgId': 3104901916173352268, 'MsgSeq': 871413208}
2025-07-31 08:37:18 | INFO | 收到表情消息: 消息ID:74387264 来自:48097389945@chatroom 发送人:xiaomaochong MD5:949c22ce23000c4fecb52cc8d2101e2d 大小:788489
2025-07-31 08:37:19 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3104901916173352268
2025-07-31 08:42:54 | DEBUG | 收到消息: {'MsgId': **********, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>命名 做运动</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>47</type>\n\t\t\t<svrid>2390666514322243175</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>xiaomaochong</chatusr>\n\t\t\t<displayname>小爱</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_kJ2461zU|v1_GxOC+LVd&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;msg&gt;&lt;emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="2" idbuffer="media*#*0_0" md5="ec659f94db17b05434544a0c377ddfc0" len="394561" productid="" androidmd5="ec659f94db17b05434544a0c377ddfc0" androidlen="394561" s60v3md5="ec659f94db17b05434544a0c377ddfc0" s60v3len="394561" s60v5md5="ec659f94db17b05434544a0c377ddfc0" s60v5len="394561" cdnurl="http*#*//vweixinf.tc.qq.com/110/20401/stodownload?m=ec659f94db17b05434544a0c377ddfc0&amp;amp;filekey=30440201010430302e02016e0402535a042065633635396639346462313762303534333435343461306333373764646663300203060541040d00000004627466730000000132&amp;amp;hy=SZ&amp;amp;storeid=26877224b0003337bf5dee8850000006e01004fb1535a29cfe011568865606&amp;amp;ef=1&amp;amp;bizid=1022" designerid="" thumburl="" encrypturl="http*#*//vweixinf.tc.qq.com/110/20402/stodownload?m=38d4d15c316659b4e890b4e3c7ac3c7c&amp;amp;filekey=30440201010430302e02016e0402535a042033386434643135633331363635396234653839306234653363376163336337630203060550040d00000004627466730000000132&amp;amp;hy=SZ&amp;amp;storeid=26877224b000417bdf5dee8850000006e02004fb2535a29cfe01156886561e&amp;amp;ef=2&amp;amp;bizid=1022" aeskey="695df9c553cb498d96d7ab1f98a32e42" externurl="http*#*//vweixinf.tc.qq.com/110/20403/stodownload?m=7cbc2c2ad453fa44f6919d2c2cee191c&amp;amp;filekey=30440201010430302e02016e0402535a042037636263326332616434353366613434663639313964326332636565313931630203009130040d00000004627466730000000132&amp;amp;hy=SZ&amp;amp;storeid=26877224b0005369ef5dee8850000006e03004fb3535a29cfe011568865632&amp;amp;ef=3&amp;amp;bizid=1022" externmd5="018e81e27adf973e3d91f2286238160a" width="450" height="450" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""&gt;&lt;/emoji&gt;&lt;/msg&gt;:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753921769</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922575, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>6de7853d1ea90e328df8f6eb5ea95943_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Voqpythh|v1_vUJcYAv5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 命名 做运动', 'NewMsgId': 2688482751094847258, 'MsgSeq': 871413209}
2025-07-31 08:42:54 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-07-31 08:42:54 | DEBUG | 使用已解析的XML处理引用消息
2025-07-31 08:42:54 | INFO | 收到引用消息: 消息ID:********** 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 内容:命名 做运动 引用类型:47
2025-07-31 08:42:54 | INFO | [DouBaoImageToImage] 收到引用消息: 命名 做运动
2025-07-31 08:42:54 | ERROR | 解析表情大小失败: junk after document element: line 1, column 1688
2025-07-31 08:42:54 | INFO | 成功保存表情映射文件，共 546 条记录
2025-07-31 08:42:54 | INFO | 成功保存表情映射文件，共 546 条记录
2025-07-31 08:42:55 | INFO | 发送文字消息: 对方wxid:48097389945@chatroom at: 内容:表情已添加触发词：做运动
2025-07-31 08:42:55 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-31 08:42:55 | INFO |   - 消息内容: 命名 做运动
2025-07-31 08:42:55 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-31 08:42:55 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-07-31 08:42:55 | INFO |   - 引用信息: {'MsgType': 47, 'Content': '<msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="2" idbuffer="media*#*0_0" md5="ec659f94db17b05434544a0c377ddfc0" len="394561" productid="" androidmd5="ec659f94db17b05434544a0c377ddfc0" androidlen="394561" s60v3md5="ec659f94db17b05434544a0c377ddfc0" s60v3len="394561" s60v5md5="ec659f94db17b05434544a0c377ddfc0" s60v5len="394561" cdnurl="http*#*//vweixinf.tc.qq.com/110/20401/stodownload?m=ec659f94db17b05434544a0c377ddfc0&amp;filekey=30440201010430302e02016e0402535a042065633635396639346462313762303534333435343461306333373764646663300203060541040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b0003337bf5dee8850000006e01004fb1535a29cfe011568865606&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http*#*//vweixinf.tc.qq.com/110/20402/stodownload?m=38d4d15c316659b4e890b4e3c7ac3c7c&amp;filekey=30440201010430302e02016e0402535a042033386434643135633331363635396234653839306234653363376163336337630203060550040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b000417bdf5dee8850000006e02004fb2535a29cfe01156886561e&amp;ef=2&amp;bizid=1022" aeskey="695df9c553cb498d96d7ab1f98a32e42" externurl="http*#*//vweixinf.tc.qq.com/110/20403/stodownload?m=7cbc2c2ad453fa44f6919d2c2cee191c&amp;filekey=30440201010430302e02016e0402535a042037636263326332616434353366613434663639313964326332636565313931630203009130040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b0005369ef5dee8850000006e03004fb3535a29cfe011568865632&amp;ef=3&amp;bizid=1022" externmd5="018e81e27adf973e3d91f2286238160a" width="450" height="450" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>:0\n', 'Msgid': '2390666514322243175', 'NewMsgId': '2390666514322243175', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '小爱', 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_kJ2461zU|v1_GxOC+LVd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753921769', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-07-31 08:42:55 | INFO |   - 引用消息ID: 
2025-07-31 08:42:55 | INFO |   - 引用消息类型: 
2025-07-31 08:42:55 | INFO |   - 引用消息内容: <msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="2" idbuffer="media*#*0_0" md5="ec659f94db17b05434544a0c377ddfc0" len="394561" productid="" androidmd5="ec659f94db17b05434544a0c377ddfc0" androidlen="394561" s60v3md5="ec659f94db17b05434544a0c377ddfc0" s60v3len="394561" s60v5md5="ec659f94db17b05434544a0c377ddfc0" s60v5len="394561" cdnurl="http*#*//vweixinf.tc.qq.com/110/20401/stodownload?m=ec659f94db17b05434544a0c377ddfc0&amp;filekey=30440201010430302e02016e0402535a042065633635396639346462313762303534333435343461306333373764646663300203060541040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b0003337bf5dee8850000006e01004fb1535a29cfe011568865606&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http*#*//vweixinf.tc.qq.com/110/20402/stodownload?m=38d4d15c316659b4e890b4e3c7ac3c7c&amp;filekey=30440201010430302e02016e0402535a042033386434643135633331363635396234653839306234653363376163336337630203060550040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b000417bdf5dee8850000006e02004fb2535a29cfe01156886561e&amp;ef=2&amp;bizid=1022" aeskey="695df9c553cb498d96d7ab1f98a32e42" externurl="http*#*//vweixinf.tc.qq.com/110/20403/stodownload?m=7cbc2c2ad453fa44f6919d2c2cee191c&amp;filekey=30440201010430302e02016e0402535a042037636263326332616434353366613434663639313964326332636565313931630203009130040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b0005369ef5dee8850000006e03004fb3535a29cfe011568865632&amp;ef=3&amp;bizid=1022" externmd5="018e81e27adf973e3d91f2286238160a" width="450" height="450" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>:0

2025-07-31 08:42:55 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-07-31 08:42:57 | DEBUG | 收到消息: {'MsgId': 1813511678, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n做运动'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922579, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_MEX/3n5C|v1_xdmwWUeq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 做运动', 'NewMsgId': 8708460154795974872, 'MsgSeq': 871413212}
2025-07-31 08:42:57 | INFO | 收到文本消息: 消息ID:1813511678 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:做运动
2025-07-31 08:42:58 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:ec659f94db17b05434544a0c377ddfc0 总长度:9992069
2025-07-31 08:42:58 | DEBUG | 处理消息内容: '做运动'
2025-07-31 08:42:58 | DEBUG | 消息内容 '做运动' 不匹配任何命令，忽略
2025-07-31 08:43:51 | DEBUG | 收到消息: {'MsgId': 1322750853, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_k7daexqs8yms22:\n做运动'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922633, 'MsgSource': '<msgsource>\n\t<sequence_id>857878012</sequence_id>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_mMIfDK8a|v1_fRPDioYD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_atU+d7o7|v1_dotoUe7A</signature>\n</msgsource>\n', 'PushContent': '\ue00d绝音 : 做运动', 'NewMsgId': 9003188257933299302, 'MsgSeq': 871413215}
2025-07-31 08:43:51 | INFO | 收到文本消息: 消息ID:1322750853 来自:48097389945@chatroom 发送人:wxid_k7daexqs8yms22 @:[] 内容:做运动
2025-07-31 08:43:52 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:ec659f94db17b05434544a0c377ddfc0 总长度:9992069
2025-07-31 08:43:52 | DEBUG | 处理消息内容: '做运动'
2025-07-31 08:43:52 | DEBUG | 消息内容 '做运动' 不匹配任何命令，忽略
2025-07-31 08:43:55 | DEBUG | 收到消息: {'MsgId': 654970176, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_k7daexqs8yms22:\n做运动'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922637, 'MsgSource': '<msgsource>\n\t<sequence_id>857878012</sequence_id>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_mMIfDK8a|v1_fRPDioYD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_+BM1byF7|v1_dFJzHctE</signature>\n</msgsource>\n', 'PushContent': '\ue00d绝音 : 做运动', 'NewMsgId': 3097833907038354541, 'MsgSeq': 871413218}
2025-07-31 08:43:55 | INFO | 收到文本消息: 消息ID:654970176 来自:48097389945@chatroom 发送人:wxid_k7daexqs8yms22 @:[] 内容:做运动
2025-07-31 08:43:56 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:ec659f94db17b05434544a0c377ddfc0 总长度:9992069
2025-07-31 08:43:56 | DEBUG | 处理消息内容: '做运动'
2025-07-31 08:43:56 | DEBUG | 消息内容 '做运动' 不匹配任何命令，忽略
2025-07-31 08:44:03 | DEBUG | 收到消息: {'MsgId': 2060345128, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="9d972db0e48015c2c7238164c8dc4bbf" len="32908" productid="" androidmd5="9d972db0e48015c2c7238164c8dc4bbf" androidlen="32908" s60v3md5="9d972db0e48015c2c7238164c8dc4bbf" s60v3len="32908" s60v5md5="9d972db0e48015c2c7238164c8dc4bbf" s60v5len="32908" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=9d972db0e48015c2c7238164c8dc4bbf&amp;filekey=30440201010430302e02016e0402535a04203964393732646230653438303135633263373233383136346338646334626266020300808c040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26619d6dd000b63f50e268e0e0000006e01004fb1535a159918e0b6c13fb22&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=db882cdf027016a97697c411e9da04e1&amp;filekey=30440201010430302e02016e0402535a042064623838326364663032373031366139373639376334313165396461303465310203008090040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26619d6dd000bca980e268e0e0000006e02004fb2535a159918e0b6c13fb29&amp;ef=2&amp;bizid=1022" aeskey="40576febe6854c3cb5a0a1c38514ebce" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=1204a5360cc1487758c87f44f8f1e282&amp;filekey=3043020101042f302d02016e0402535a0420313230346135333630636331343837373538633837663434663866316532383202022410040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26619d6dd000c87d90e268e0e0000006e03004fb3535a159918e0b6c13fb3c&amp;ef=3&amp;bizid=1022" externmd5="b3f556d1ec7a8de829d370113c8bd306" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922644, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_OnfL7MyT|v1_yjHpK3Lf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 213646346429408669, 'MsgSeq': 871413221}
2025-07-31 08:44:03 | INFO | 收到表情消息: 消息ID:2060345128 来自:48097389945@chatroom 发送人:last--exile MD5:9d972db0e48015c2c7238164c8dc4bbf 大小:32908
2025-07-31 08:44:03 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 213646346429408669
2025-07-31 08:44:06 | DEBUG | 收到消息: {'MsgId': 535095390, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="9e47c7ea16ad83b5da37218f23a1cfb9" cdnvideourl="3057020100044b304902010002049363814102032f514902048d3122750204688abbf7042439306265393562322d646436312d346139362d383730302d3565316565313564343861650204052408040201000405004c55cd00" cdnthumbaeskey="9e47c7ea16ad83b5da37218f23a1cfb9" cdnthumburl="3057020100044b304902010002049363814102032f514902048d3122750204688abbf7042439306265393562322d646436312d346139362d383730302d3565316565313564343861650204052408040201000405004c55cd00" length="1104417" playlength="8" cdnthumblength="20122" cdnthumbwidth="364" cdnthumbheight="720" fromusername="xiaomaochong" md5="201f58de6d170463695201e67cd29982" newmd5="3147241ab1d159c17c17681201de8b05" isplaceholder="0" rawmd5="828dc23e6a0ae8c7c0e7d6f7ca742a52" rawlength="5575013" cdnrawvideourl="3056020100044a304802010002035a663f02032f514902041f3122750204688abc54042431323763613237382d653364622d346133332d626661382d3862373664643766623362630204059800040201000405004c537500" cdnrawvideoaeskey="788389e2950bc9a0972d052982ee2c41" overwritenewmsgid="0" originsourcemd5="bcee575a1ebe0a46f44406c1d7e681ae" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922647, 'MsgSource': '<msgsource>\n\t<videopreloadlen>698757</videopreloadlen>\n\t<sec_msg_node>\n\t\t<uuid>a89fb741ef8bc13cc590454a61f20ff4_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_jjG3TlNv|v1_mR7p+/6G</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段视频', 'NewMsgId': 2993665763065348804, 'MsgSeq': 871413222}
2025-07-31 08:44:06 | INFO | 收到视频消息: 消息ID:535095390 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="9e47c7ea16ad83b5da37218f23a1cfb9" cdnvideourl="3057020100044b304902010002049363814102032f514902048d3122750204688abbf7042439306265393562322d646436312d346139362d383730302d3565316565313564343861650204052408040201000405004c55cd00" cdnthumbaeskey="9e47c7ea16ad83b5da37218f23a1cfb9" cdnthumburl="3057020100044b304902010002049363814102032f514902048d3122750204688abbf7042439306265393562322d646436312d346139362d383730302d3565316565313564343861650204052408040201000405004c55cd00" length="1104417" playlength="8" cdnthumblength="20122" cdnthumbwidth="364" cdnthumbheight="720" fromusername="xiaomaochong" md5="201f58de6d170463695201e67cd29982" newmd5="3147241ab1d159c17c17681201de8b05" isplaceholder="0" rawmd5="828dc23e6a0ae8c7c0e7d6f7ca742a52" rawlength="5575013" cdnrawvideourl="3056020100044a304802010002035a663f02032f514902041f3122750204688abc54042431323763613237382d653364622d346133332d626661382d3862373664643766623362630204059800040201000405004c537500" cdnrawvideoaeskey="788389e2950bc9a0972d052982ee2c41" overwritenewmsgid="0" originsourcemd5="bcee575a1ebe0a46f44406c1d7e681ae" isad="0" />
</msg>

2025-07-31 08:44:17 | DEBUG | 收到消息: {'MsgId': 1817072448, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'last--exile:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>命名 看戏</title>\n\t\t<des />\n\t\t<action />\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url />\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<songalbumurl />\n\t\t<songlyric />\n\t\t<template_id />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<fileext />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<thumburl />\n\t\t<md5 />\n\t\t<statextstr />\n\t\t<refermsg>\n\t\t\t<type>47</type>\n\t\t\t<svrid>213646346429408669</svrid>\n\t\t\t<fromusr>last--exile</fromusr>\n\t\t\t<chatusr>last--exile</chatusr>\n\t\t\t<displayname>亮</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;alnode&gt;&lt;fr&gt;1&lt;/fr&gt;&lt;/alnode&gt;&lt;/msgsource&gt;</msgsource>\n\t\t\t<createtime>1753922644</createtime>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;&lt;msg&gt;&lt;emoji md5="9d972db0e48015c2c7238164c8dc4bbf" type="2" len="9227" width="240" height="240"/&gt;&lt;gameext type="0" content="0"/&gt;&lt;/msg&gt;</content>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>last--exile</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922658, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>cac0848c83eb53933010747eb44b0c68_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PeOW4+QV|v1_/m05Qx3r</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : 命名 看戏', 'NewMsgId': 1003382120440546611, 'MsgSeq': 871413223}
2025-07-31 08:44:17 | DEBUG | 从群聊消息中提取发送者: last--exile
2025-07-31 08:44:17 | DEBUG | 使用已解析的XML处理引用消息
2025-07-31 08:44:17 | INFO | 收到引用消息: 消息ID:1817072448 来自:48097389945@chatroom 发送人:last--exile 内容:命名 看戏 引用类型:47
2025-07-31 08:44:17 | INFO | [DouBaoImageToImage] 收到引用消息: 命名 看戏
2025-07-31 08:44:17 | INFO | 成功保存表情映射文件，共 546 条记录
2025-07-31 08:44:17 | INFO | 成功保存表情映射文件，共 546 条记录
2025-07-31 08:44:18 | INFO | 发送文字消息: 对方wxid:48097389945@chatroom at: 内容:表情已添加触发词：看戏
2025-07-31 08:44:18 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-31 08:44:18 | INFO |   - 消息内容: 命名 看戏
2025-07-31 08:44:18 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-31 08:44:18 | INFO |   - 发送人: last--exile
2025-07-31 08:44:18 | INFO |   - 引用信息: {'MsgType': 47, 'Content': '<?xml version="1.0"?><msg><emoji md5="9d972db0e48015c2c7238164c8dc4bbf" type="2" len="9227" width="240" height="240"/><gameext type="0" content="0"/></msg>', 'Msgid': '213646346429408669', 'NewMsgId': '213646346429408669', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '亮', 'MsgSource': '<msgsource><alnode><fr>1</fr></alnode></msgsource>', 'Createtime': '1753922644', 'SenderWxid': 'last--exile'}
2025-07-31 08:44:18 | INFO |   - 引用消息ID: 
2025-07-31 08:44:18 | INFO |   - 引用消息类型: 
2025-07-31 08:44:18 | INFO |   - 引用消息内容: <?xml version="1.0"?><msg><emoji md5="9d972db0e48015c2c7238164c8dc4bbf" type="2" len="9227" width="240" height="240"/><gameext type="0" content="0"/></msg>
2025-07-31 08:44:18 | INFO |   - 引用消息发送人: last--exile
2025-07-31 08:44:22 | DEBUG | 收到消息: {'MsgId': 41428067, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n看戏'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922664, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_zt0Kag5Y|v1_BIO9JSCc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : 看戏', 'NewMsgId': 6070987344082956440, 'MsgSeq': 871413226}
2025-07-31 08:44:22 | INFO | 收到文本消息: 消息ID:41428067 来自:48097389945@chatroom 发送人:last--exile @:[] 内容:看戏
2025-07-31 08:44:23 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:582ab6ed89fbcd7a2dca3b07d5a21cf7 总长度:9992069
2025-07-31 08:44:23 | DEBUG | 处理消息内容: '看戏'
2025-07-31 08:44:23 | DEBUG | 消息内容 '看戏' 不匹配任何命令，忽略
2025-07-31 08:44:37 | DEBUG | 收到消息: {'MsgId': 544703780, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n看戏'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922678, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_9Ve4hAxS|v1_nqkpFJ8o</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : 看戏', 'NewMsgId': 3426309594664272948, 'MsgSeq': 871413229}
2025-07-31 08:44:37 | INFO | 收到文本消息: 消息ID:544703780 来自:48097389945@chatroom 发送人:last--exile @:[] 内容:看戏
2025-07-31 08:44:37 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:582ab6ed89fbcd7a2dca3b07d5a21cf7 总长度:9992069
2025-07-31 08:44:37 | DEBUG | 处理消息内容: '看戏'
2025-07-31 08:44:37 | DEBUG | 消息内容 '看戏' 不匹配任何命令，忽略
2025-07-31 08:44:40 | DEBUG | 收到消息: {'MsgId': 2089072696, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_k7daexqs8yms22:\n随机表情'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922681, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Iv3YSpam|v1_Yc70pBBa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '\ue00d绝音 : 随机表情', 'NewMsgId': 7353163451799926001, 'MsgSeq': 871413232}
2025-07-31 08:44:40 | INFO | 收到文本消息: 消息ID:2089072696 来自:48097389945@chatroom 发送人:wxid_k7daexqs8yms22 @:[] 内容:随机表情
2025-07-31 08:44:40 | DEBUG | 处理消息内容: '随机表情'
2025-07-31 08:44:40 | DEBUG | 消息内容 '随机表情' 不匹配任何命令，忽略
2025-07-31 08:44:58 | DEBUG | 收到消息: {'MsgId': 878554891, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'lyj123456zl:\n60s'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922700, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_L4ROVUPs|v1_ILRzeulF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿杰 : 60s', 'NewMsgId': 3520014481515103826, 'MsgSeq': 871413233}
2025-07-31 08:44:58 | INFO | 收到文本消息: 消息ID:878554891 来自:48097389945@chatroom 发送人:lyj123456zl @:[] 内容:60s
2025-07-31 08:44:59 | DEBUG | 处理消息内容: '60s'
2025-07-31 08:44:59 | DEBUG | 消息内容 '60s' 不匹配任何命令，忽略
2025-07-31 08:45:04 | DEBUG | 收到消息: {'MsgId': 1518945963, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="62767a667174796270696962786d6c6b" encryver="0" cdnthumbaeskey="62767a667174796270696962786d6c6b" cdnthumburl="3057020100044b3049020100020499f11ce202033d11fe02047ad0533b0204688abc91042465343039623231322d353234342d346636662d383163632d3963366362386236316262310204052428010201000405004c50ba00b67695f5" cdnthumblength="3975" cdnthumbheight="100" cdnthumbwidth="56" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020499f11ce202033d11fe02047ad0533b0204688abc91042465343039623231322d353234342d346636662d383163632d3963366362386236316262310204052428010201000405004c50ba00b67695f5" length="206772" cdnbigimgurl="3057020100044b3049020100020499f11ce202033d11fe02047ad0533b0204688abc91042465343039623231322d353234342d346636662d383163632d3963366362386236316262310204052428010201000405004c50ba00b67695f5" hdlength="547091" md5="44970efb01bf1c1215626e6f8cc9014d">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922705, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>58ce8eb8acf1bd494c788250fcebc5fb_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_nJ38GEy0|v1_AP+0+lIL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110在群聊中发了一张图片', 'NewMsgId': 6035985009937341133, 'MsgSeq': 871413234}
2025-07-31 08:45:04 | INFO | 收到图片消息: 消息ID:1518945963 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 XML:<?xml version="1.0"?><msg><img aeskey="62767a667174796270696962786d6c6b" encryver="0" cdnthumbaeskey="62767a667174796270696962786d6c6b" cdnthumburl="3057020100044b3049020100020499f11ce202033d11fe02047ad0533b0204688abc91042465343039623231322d353234342d346636662d383163632d3963366362386236316262310204052428010201000405004c50ba00b67695f5" cdnthumblength="3975" cdnthumbheight="100" cdnthumbwidth="56" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020499f11ce202033d11fe02047ad0533b0204688abc91042465343039623231322d353234342d346636662d383163632d3963366362386236316262310204052428010201000405004c50ba00b67695f5" length="206772" cdnbigimgurl="3057020100044b3049020100020499f11ce202033d11fe02047ad0533b0204688abc91042465343039623231322d353234342d346636662d383163632d3963366362386236316262310204052428010201000405004c50ba00b67695f5" hdlength="547091" md5="44970efb01bf1c1215626e6f8cc9014d"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 08:45:05 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-31 08:45:05 | INFO | [TimerTask] 缓存图片消息: 1518945963
2025-07-31 08:45:12 | DEBUG | 收到消息: {'MsgId': 1945293604, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="199644eeafff25f31c42c9bc33a41c9e" encryver="1" cdnthumbaeskey="199644eeafff25f31c42c9bc33a41c9e" cdnthumburl="3057020100044b304902010002049c5da34302032e1d7b0204826196240204688abc99042437656165313732302d626435622d346464632d386464642d356263326161306461316565020405252a010201000405004c4e6300" cdnthumblength="6107" cdnthumbheight="144" cdnthumbwidth="57" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e1d7b0204826196240204688abc99042437656165313732302d626435622d346464632d386464642d356263326161306461316565020405252a010201000405004c4e6300" length="36118" cdnbigimgurl="3057020100044b304902010002049c5da34302032e1d7b0204826196240204688abc99042437656165313732302d626435622d346464632d386464642d356263326161306461316565020405252a010201000405004c4e6300" hdlength="796729" md5="1fb79183fab11c072e42b6dbd05e13db" hevc_mid_size="36118" originsourcemd5="1fb79183fab11c072e42b6dbd05e13db">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wxb0eef1f67b7a2949</appid>\n\t\t<version>4</version>\n\t\t<appname>国家反诈中心</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922714, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>ee6f53ca996da18d43ffc76e985acfd4_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_SGACVBtE|v1_E7TSCc7w</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 6360636012539445717, 'MsgSeq': 871413235}
2025-07-31 08:45:12 | INFO | 收到图片消息: 消息ID:1945293604 来自:48097389945@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="199644eeafff25f31c42c9bc33a41c9e" encryver="1" cdnthumbaeskey="199644eeafff25f31c42c9bc33a41c9e" cdnthumburl="3057020100044b304902010002049c5da34302032e1d7b0204826196240204688abc99042437656165313732302d626435622d346464632d386464642d356263326161306461316565020405252a010201000405004c4e6300" cdnthumblength="6107" cdnthumbheight="144" cdnthumbwidth="57" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e1d7b0204826196240204688abc99042437656165313732302d626435622d346464632d386464642d356263326161306461316565020405252a010201000405004c4e6300" length="36118" cdnbigimgurl="3057020100044b304902010002049c5da34302032e1d7b0204826196240204688abc99042437656165313732302d626435622d346464632d386464642d356263326161306461316565020405252a010201000405004c4e6300" hdlength="796729" md5="1fb79183fab11c072e42b6dbd05e13db" hevc_mid_size="36118" originsourcemd5="1fb79183fab11c072e42b6dbd05e13db"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wxb0eef1f67b7a2949</appid><version>4</version><appname>国家反诈中心</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 08:45:13 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-31 08:45:13 | INFO | [TimerTask] 缓存图片消息: 1945293604
2025-07-31 08:46:15 | DEBUG | 收到消息: {'MsgId': 1889864761, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n你那个好就是关键词了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922777, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_+Gm2pX17|v1_kEXBncWi</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 你那个好就是关键词了', 'NewMsgId': 5035258904141044314, 'MsgSeq': 871413236}
2025-07-31 08:46:15 | INFO | 收到文本消息: 消息ID:1889864761 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:你那个好就是关键词了
2025-07-31 08:46:16 | DEBUG | 处理消息内容: '你那个好就是关键词了'
2025-07-31 08:46:16 | DEBUG | 消息内容 '你那个好就是关键词了' 不匹配任何命令，忽略
2025-07-31 08:46:22 | DEBUG | 收到消息: {'MsgId': 818567408, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n还加一个做运动干啥'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922784, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_dVUjRxAN|v1_SdUrG+Sr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 还加一个做运动干啥', 'NewMsgId': 789536975175118613, 'MsgSeq': 871413237}
2025-07-31 08:46:22 | INFO | 收到文本消息: 消息ID:818567408 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:还加一个做运动干啥
2025-07-31 08:46:23 | DEBUG | 处理消息内容: '还加一个做运动干啥'
2025-07-31 08:46:23 | DEBUG | 消息内容 '还加一个做运动干啥' 不匹配任何命令，忽略
2025-07-31 08:46:25 | DEBUG | 收到消息: {'MsgId': 137566679, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n好'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922785, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ArcEvK+J|v1_cxNV5nXP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 好', 'NewMsgId': 7952646624591402487, 'MsgSeq': 871413238}
2025-07-31 08:46:25 | INFO | 收到文本消息: 消息ID:137566679 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:好
2025-07-31 08:46:25 | DEBUG | 处理消息内容: '好'
2025-07-31 08:46:25 | DEBUG | 消息内容 '好' 不匹配任何命令，忽略
2025-07-31 08:46:59 | DEBUG | 收到消息: {'MsgId': 210336083, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="817a155813810974c32cf6482f2f9758" encryver="1" cdnthumbaeskey="817a155813810974c32cf6482f2f9758" cdnthumburl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688abd04042465323064633432302d313262302d343131352d613062622d396163623831626633343738020405290a020201000405004c4d3600" cdnthumblength="3333" cdnthumbheight="432" cdnthumbwidth="244" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688abd04042465323064633432302d313262302d343131352d613062622d396163623831626633343738020405290a020201000405004c4d3600" length="1318729" md5="df2a9d6022eaed17997c89479b1bf6c3" hevc_mid_size="189808" originsourcemd5="ca3293049ed1b2ba325e5a50f22ae1ed">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjMwNjAzMDEwMTAwMDAwMDAiLCJwZHFIYXNoIjoiNWJiNTJiMjVhYjM1YjMzNTUz\nMzU2YjMxYTUzMzk1MmI5MmFiYmI1MzkxMjNiMmEzYjM1MzEzNmIzMjIxMTM3MSJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922820, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>0dcedf21beb81c5eb5e9f2739e16acfb_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_vEZLViQ7|v1_bJqTwpvM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一张图片', 'NewMsgId': 5140840297583429368, 'MsgSeq': 871413239}
2025-07-31 08:46:59 | INFO | 收到图片消息: 消息ID:210336083 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 XML:<?xml version="1.0"?><msg><img aeskey="817a155813810974c32cf6482f2f9758" encryver="1" cdnthumbaeskey="817a155813810974c32cf6482f2f9758" cdnthumburl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688abd04042465323064633432302d313262302d343131352d613062622d396163623831626633343738020405290a020201000405004c4d3600" cdnthumblength="3333" cdnthumbheight="432" cdnthumbwidth="244" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688abd04042465323064633432302d313262302d343131352d613062622d396163623831626633343738020405290a020201000405004c4d3600" length="1318729" md5="df2a9d6022eaed17997c89479b1bf6c3" hevc_mid_size="189808" originsourcemd5="ca3293049ed1b2ba325e5a50f22ae1ed"><secHashInfoBase64>eyJwaGFzaCI6IjMwNjAzMDEwMTAwMDAwMDAiLCJwZHFIYXNoIjoiNWJiNTJiMjVhYjM1YjMzNTUzMzU2YjMxYTUzMzk1MmI5MmFiYmI1MzkxMjNiMmEzYjM1MzEzNmIzMjIxMTM3MSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 08:47:00 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-31 08:47:00 | INFO | [TimerTask] 缓存图片消息: 210336083
2025-07-31 08:47:00 | DEBUG | 收到消息: {'MsgId': 1857385577, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n出门了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922822, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PDJBwOYW|v1_+a1XcV3C</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 出门了', 'NewMsgId': 5017502903489933544, 'MsgSeq': 871413240}
2025-07-31 08:47:00 | INFO | 收到文本消息: 消息ID:1857385577 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:出门了
2025-07-31 08:47:01 | DEBUG | 处理消息内容: '出门了'
2025-07-31 08:47:01 | DEBUG | 消息内容 '出门了' 不匹配任何命令，忽略
2025-07-31 08:47:03 | DEBUG | 收到消息: {'MsgId': 1209691816, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n有点晚'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922825, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_vuuxD8g9|v1_CNnhIsoK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 有点晚', 'NewMsgId': 3372730862807432589, 'MsgSeq': 871413241}
2025-07-31 08:47:03 | INFO | 收到文本消息: 消息ID:1209691816 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:有点晚
2025-07-31 08:47:04 | DEBUG | 处理消息内容: '有点晚'
2025-07-31 08:47:04 | DEBUG | 消息内容 '有点晚' 不匹配任何命令，忽略
2025-07-31 08:47:11 | DEBUG | 收到消息: {'MsgId': 2066525268, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n会不会迟到就看有没有车子了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922833, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_tXRJvBVn|v1_0HWTVsUI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 会不会迟到就看有没有车子了', 'NewMsgId': 3589866956970006479, 'MsgSeq': 871413242}
2025-07-31 08:47:11 | INFO | 收到文本消息: 消息ID:2066525268 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:会不会迟到就看有没有车子了
2025-07-31 08:47:12 | DEBUG | 处理消息内容: '会不会迟到就看有没有车子了'
2025-07-31 08:47:12 | DEBUG | 消息内容 '会不会迟到就看有没有车子了' 不匹配任何命令，忽略
2025-07-31 08:47:42 | DEBUG | 收到消息: {'MsgId': 409593316, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n看着没得啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922863, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Ga+zhmQ0|v1_SImP58di</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 看着没得啊', 'NewMsgId': 6687614789520194271, 'MsgSeq': 871413243}
2025-07-31 08:47:42 | INFO | 收到文本消息: 消息ID:409593316 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:看着没得啊
2025-07-31 08:47:42 | DEBUG | 处理消息内容: '看着没得啊'
2025-07-31 08:47:42 | DEBUG | 消息内容 '看着没得啊' 不匹配任何命令，忽略
2025-07-31 08:47:44 | DEBUG | 收到消息: {'MsgId': 486309951, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'xiaomaochong:\n<msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="91b0b203259d0c9cb86eca35beba4d73" len="2269646" productid="" androidmd5="91b0b203259d0c9cb86eca35beba4d73" androidlen="2269646" s60v3md5="91b0b203259d0c9cb86eca35beba4d73" s60v3len="2269646" s60v5md5="91b0b203259d0c9cb86eca35beba4d73" s60v5len="2269646" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=91b0b203259d0c9cb86eca35beba4d73&amp;filekey=30440201010430302e02016e0402535a04203931623062323033323539643063396362383665636133356265626134643733020322a1ce040d00000004627466730000000132&amp;hy=SZ&amp;storeid=268725331000e34ac1fe6aeb70000006e01004fb1535a21b4a8809058bde62&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=956693299d62d3261ce81742848cf5c1&amp;filekey=30440201010430302e02016e0402535a04203935363639333239396436326433323631636538313734323834386366356331020322a1d0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2687253320001dd091fe6aeb70000006e02004fb2535a21b4a8809058bde92&amp;ef=2&amp;bizid=1022" aeskey="045dc30d39804be1b7ccc6a1edc04bd5" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=0efdb3f78a49eed4eff18f5c60853999&amp;filekey=30440201010430302e02016e0402535a042030656664623366373861343965656434656666313866356336303835333939390203016a10040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26872533200047b3c1fe6aeb70000006e03004fb3535a21b4a8809058bdec0&amp;ef=3&amp;bizid=1022" externmd5="cbccc6e867e4d2138aecacfed1cac7d7" width="320" height="320" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922866, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_M0iBjJPD|v1_wpC8oZiN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一个表情', 'NewMsgId': 4063027138291172739, 'MsgSeq': 871413244}
2025-07-31 08:47:44 | INFO | 收到表情消息: 消息ID:486309951 来自:48097389945@chatroom 发送人:xiaomaochong MD5:91b0b203259d0c9cb86eca35beba4d73 大小:2269646
2025-07-31 08:47:45 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4063027138291172739
2025-07-31 08:48:11 | DEBUG | 收到消息: {'MsgId': 1003589643, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n还没出村里'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922893, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_onNJZUpG|v1_Mv7nnAEI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 还没出村里', 'NewMsgId': 9131024939314670064, 'MsgSeq': 871413245}
2025-07-31 08:48:11 | INFO | 收到文本消息: 消息ID:1003589643 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:还没出村里
2025-07-31 08:48:11 | DEBUG | 处理消息内容: '还没出村里'
2025-07-31 08:48:11 | DEBUG | 消息内容 '还没出村里' 不匹配任何命令，忽略
2025-07-31 08:48:15 | DEBUG | 收到消息: {'MsgId': 1359460245, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n出了才有'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922897, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_zWhifeXn|v1_IDw4y3Bn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 出了才有', 'NewMsgId': 6968607401022577639, 'MsgSeq': 871413246}
2025-07-31 08:48:15 | INFO | 收到文本消息: 消息ID:1359460245 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:出了才有
2025-07-31 08:48:15 | DEBUG | 处理消息内容: '出了才有'
2025-07-31 08:48:15 | DEBUG | 消息内容 '出了才有' 不匹配任何命令，忽略
2025-07-31 08:48:21 | DEBUG | 收到消息: {'MsgId': 593067590, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n单车不让进村'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922902, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_LYQQqD9Y|v1_EzQqF1P3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 单车不让进村', 'NewMsgId': 8680944972266130590, 'MsgSeq': 871413247}
2025-07-31 08:48:21 | INFO | 收到文本消息: 消息ID:593067590 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:单车不让进村
2025-07-31 08:48:21 | DEBUG | 处理消息内容: '单车不让进村'
2025-07-31 08:48:21 | DEBUG | 消息内容 '单车不让进村' 不匹配任何命令，忽略
2025-07-31 08:48:46 | DEBUG | 收到消息: {'MsgId': 959470819, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ohq9p1qosjzq22:\n唱舞签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922928, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_db1vqrB8|v1_U+MJy/RI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8360581064062657747, 'MsgSeq': 871413248}
2025-07-31 08:48:46 | INFO | 收到文本消息: 消息ID:959470819 来自:27852221909@chatroom 发送人:wxid_ohq9p1qosjzq22 @:[] 内容:唱舞签到
2025-07-31 08:48:46 | INFO | 发送链接消息: 对方wxid:27852221909@chatroom 链接:https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx6de432d4ad7e151c&redirect_uri=http%3A%2F%2Freserve.fhsj.xipu.com%2Fapi%2Fsign%2Findex&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect 标题:唱舞全明星 描述:点击进入签到页面，领取专属福利 缩略图链接:https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUBlv9G75UTulanktDF1OxFO7Wyhzs1WS609tq1j9icfNhLkM6zB3lwM5ZlbgQia1ibIcxuj35WAm465w/640?wxtype=jpeg&wxfrom=0
2025-07-31 08:48:46 | DEBUG | 处理消息内容: '唱舞签到'
2025-07-31 08:48:46 | DEBUG | 消息内容 '唱舞签到' 不匹配任何命令，忽略
2025-07-31 08:49:43 | DEBUG | 收到消息: {'MsgId': 1735170800, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n做运动'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922985, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_jd3iSxX8|v1_1hZAXZEH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 做运动', 'NewMsgId': 477550346174209999, 'MsgSeq': 871413251}
2025-07-31 08:49:43 | INFO | 收到文本消息: 消息ID:1735170800 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:做运动
2025-07-31 08:49:44 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:ec659f94db17b05434544a0c377ddfc0 总长度:9992069
2025-07-31 08:49:44 | DEBUG | 处理消息内容: '做运动'
2025-07-31 08:49:44 | DEBUG | 消息内容 '做运动' 不匹配任何命令，忽略
2025-07-31 08:49:55 | DEBUG | 收到消息: {'MsgId': 638278098, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n有车子'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922997, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_mVx3H03R|v1_JDWVGJ6m</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 有车子', 'NewMsgId': 1889470718369188221, 'MsgSeq': 871413254}
2025-07-31 08:49:55 | INFO | 收到文本消息: 消息ID:638278098 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:有车子
2025-07-31 08:49:56 | DEBUG | 处理消息内容: '有车子'
2025-07-31 08:49:56 | DEBUG | 消息内容 '有车子' 不匹配任何命令，忽略
2025-07-31 08:49:58 | DEBUG | 收到消息: {'MsgId': 126661664, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n有了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753922999, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Sg3kSgdi|v1_HdBbrLNS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 有了', 'NewMsgId': 3136118717375587778, 'MsgSeq': 871413255}
2025-07-31 08:49:58 | INFO | 收到文本消息: 消息ID:126661664 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:有了
2025-07-31 08:49:59 | DEBUG | 处理消息内容: '有了'
2025-07-31 08:49:59 | DEBUG | 消息内容 '有了' 不匹配任何命令，忽略
2025-07-31 08:50:01 | DEBUG | 收到消息: {'MsgId': 1281316480, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n走了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923001, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_H6Bk3tHZ|v1_E8nmDw+X</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 走了', 'NewMsgId': 6553633525652281681, 'MsgSeq': 871413256}
2025-07-31 08:50:01 | INFO | 收到文本消息: 消息ID:1281316480 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:走了
2025-07-31 08:50:01 | DEBUG | 处理消息内容: '走了'
2025-07-31 08:50:01 | DEBUG | 消息内容 '走了' 不匹配任何命令，忽略
2025-07-31 08:50:04 | DEBUG | 收到消息: {'MsgId': 276197814, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>我删了</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>5035258904141044314</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_wlnzvr8ivgd422</chatusr>\n\t\t\t<displayname>锦岚</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_OW6J1NPv|v1_lsTSgHRF&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n你那个好就是关键词了</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753922777</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923002, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>9e6a5b1605be3f39baf5e93db2089a2a_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_pOh3et0s|v1_EOTD+4e3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 我删了', 'NewMsgId': 5687708189815849369, 'MsgSeq': 871413257}
2025-07-31 08:50:04 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-07-31 08:50:04 | DEBUG | 使用已解析的XML处理引用消息
2025-07-31 08:50:04 | INFO | 收到引用消息: 消息ID:276197814 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 内容:我删了 引用类型:1
2025-07-31 08:50:04 | INFO | [DouBaoImageToImage] 收到引用消息: 我删了
2025-07-31 08:50:04 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-31 08:50:04 | INFO |   - 消息内容: 我删了
2025-07-31 08:50:04 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-31 08:50:04 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-07-31 08:50:04 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n你那个好就是关键词了', 'Msgid': '5035258904141044314', 'NewMsgId': '5035258904141044314', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '锦岚', 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_OW6J1NPv|v1_lsTSgHRF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753922777', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-07-31 08:50:04 | INFO |   - 引用消息ID: 
2025-07-31 08:50:04 | INFO |   - 引用消息类型: 
2025-07-31 08:50:04 | INFO |   - 引用消息内容: 
你那个好就是关键词了
2025-07-31 08:50:04 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-07-31 08:50:09 | DEBUG | 收到消息: {'MsgId': 247750134, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n公司见～'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923010, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_G9lXpOE/|v1_VjBWR0cy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 公司见～', 'NewMsgId': 7151477454548493514, 'MsgSeq': 871413258}
2025-07-31 08:50:09 | INFO | 收到文本消息: 消息ID:247750134 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:公司见～
2025-07-31 08:50:09 | DEBUG | 处理消息内容: '公司见～'
2025-07-31 08:50:09 | DEBUG | 消息内容 '公司见～' 不匹配任何命令，忽略
2025-07-31 08:50:14 | DEBUG | 收到消息: {'MsgId': 1311900861, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n其他群不适用'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923016, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_EDViejX4|v1_KpoRpqsD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 其他群不适用', 'NewMsgId': 7234778445094189835, 'MsgSeq': 871413259}
2025-07-31 08:50:14 | INFO | 收到文本消息: 消息ID:1311900861 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:其他群不适用
2025-07-31 08:50:15 | DEBUG | 处理消息内容: '其他群不适用'
2025-07-31 08:50:15 | DEBUG | 消息内容 '其他群不适用' 不匹配任何命令，忽略
2025-07-31 08:50:20 | DEBUG | 收到消息: {'MsgId': 1298980140, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n[破涕为笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923022, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_tvQ41ooU|v1_/hvtHxDo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : [破涕为笑]', 'NewMsgId': 7326232965712084399, 'MsgSeq': 871413260}
2025-07-31 08:50:20 | INFO | 收到表情消息: 消息ID:1298980140 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:[破涕为笑]
2025-07-31 08:50:20 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7326232965712084399
2025-07-31 08:52:08 | DEBUG | 收到消息: {'MsgId': 638582521, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_e3o8s2nf9u2o22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>当前版本不支持展示该内容，请升级至最新版本。</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>51</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderFeed>\n\t\t\t<objectId>14712642907883575745</objectId>\n\t\t\t<objectNonceId>6998837226719791102_4_20_13_1_1753922633353692_6ec32c0e-6da7-11f0-91fa-c76ce9a68e23</objectNonceId>\n\t\t\t<feedType>4</feedType>\n\t\t\t<nickname>整活儿小胖橘</nickname>\n\t\t\t<username>v2_060000231003b20faec8c6e58f1ecad5c700e932b077aeb001cd0e915ce822f980272cce55bb@finder</username>\n\t\t\t<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/Z8nXrOrpYyRd9EQE3R5D6plEIKosjzsVkQoVZA3DJ60Djp75l6DYILQIAqIgicubNUpqQeWvrzHmDmak9HrGQo51bhyWMKSY4wGodzoqrSicAibK4jichsF6cGb8nMDKnXOV1FeO7bJib7U8bXoxjicYeic4g/0]]></avatar>\n\t\t\t<desc>狗子:一想到等会儿要干什么 我就想笑！#搞笑 #狗狗 #万万没想到 #迷惑行为大赏</desc>\n\t\t\t<mediaCount>1</mediaCount>\n\t\t\t<localId>0</localId>\n\t\t\t<authIconType>0</authIconType>\n\t\t\t<authIconUrl><![CDATA[]]></authIconUrl>\n\t\t\t<mediaList>\n\t\t\t\t<media>\n\t\t\t\t\t<mediaType>4</mediaType>\n\t\t\t\t\t<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eez3Y79SxtvVL0L7CkPM6dFibFeI6caGYwFHqyGNEzNfZnIuEjSBLDQQHqJ6ibTibMw4SWcMGXHodIm0jmhxdibhiabqkr5h3DRM1Rf0WT9u3BQNyTYK65sRWsQ66BYhURJy3k8N84F3s8bvuAg&hy=SH&idx=1&m=7facb544bb33a256e46b012a6032e609&uzid=7a22e&token=6xykWLEnztJD1V02HxcJfWTOeLaomgcSN64fDgQLgjfRdh7EIvItdMLrQVYMHT3JngYnsibYOs386PEnK3VEAqBicaBoMm8XQjQTOHKLQDsod7wLaPJENfzkv8zXEh4obe6uW3ia3cyv5UwoVa5Z2OvRTy6jmpeJJNLhWxXuDpVKrA&basedata=CAESBnhXVDEyOBoGeFdUMTExGgZ4V1QxMTIaBnhXVDEyNhoGeFdUMTEzGgZ4V1QxMjcaBnhXVDEyOCIMCgoKBnhXVDExMhABKgcIvR4QABgC&sign=zGqchF5rnSWarCZzrwqN5E-5JkLL22BxkaF1LqAO4nCgZS1YNcmEQ43krBnSKCJpbdFf7XcJJnDu3Etuf-fh9A&ctsc=20&extg=108b900&ftype=201&svrbypass=AAuL%2FQsFAAABAAAAAAD8azfgas7%2B4575TLyKaBAAAADnaHZTnGbFfAj9RgZXfw6VEjo4ZHYE3hi3B%2FSS7pencRyl44AaMVeyiMoMbnzw0bLwQC74fTmyh9U%3D&svrnonce=1753922636]]></url>\n\t\t\t\t\t<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvsMKVJgw3kjUibmianEkBPjKwOOAiaeOSG9mkYS72jOqKzZ99HZDrw3zqbxg7jeHq8DvZJNJ76XV6gpasrIWeamkzQzbiafGiaEDickB2y3soDOAEU&hy=SH&idx=1&m=8cea9af5c32f391cd553a14c3fea4b4a&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxhTr34vic2gZkpDkEeBOHiaOCZCZSpCdxD2JBuktP9VfOpfCOQxruGnoVNu7uQh7juufgE5AkZstxTF63Sfo7AaZkDbWT2wiaP2OKr2JQIATv6nluHbb5oREyBwqEuZP3tPFUxqzmbhntpu&ctsc=2-20]]></thumbUrl>\n\t\t\t\t\t<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvsMKVJgw3kjUibmianEkBPjKwOOAiaeOSG9mkYS72jOqKzZ99HZDrw3zqbxg7jeHq8DvZJNJ76XV6gpasrIWeamkzQzbiafGiaEDickB2y3soDOAEU&hy=SH&idx=1&m=8cea9af5c32f391cd553a14c3fea4b4a&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxhTr34vic2gZkpDkEeBOHiaOCZCZSpCdxD2JBuktP9VfOpfCOQxruGnoVNu7uQh7juufgE5AkZstxTF63Sfo7AaZkDbWT2wiaP2OKr2JQIATv6nluHbb5oREyBwqEuZP3tPFUxqzmbhntpu&ctsc=2-20]]></coverUrl>\n\t\t\t\t\t<fullCoverUrl><![CDATA[]]></fullCoverUrl>\n\t\t\t\t\t<fullClipInset><![CDATA[]]></fullClipInset>\n\t\t\t\t\t<width>1080.0</width>\n\t\t\t\t\t<height>1920.0</height>\n\t\t\t\t\t<videoPlayDuration>15</videoPlayDuration>\n\t\t\t\t</media>\n\t\t\t</mediaList>\n\t\t\t<megaVideo>\n\t\t\t\t<objectId />\n\t\t\t\t<objectNonceId />\n\t\t\t</megaVideo>\n\t\t\t<bizUsername />\n\t\t\t<bizNickname />\n\t\t\t<bizAvatar><![CDATA[]]></bizAvatar>\n\t\t\t<bizUsernameV2 />\n\t\t\t<bizAuthIconType>0</bizAuthIconType>\n\t\t\t<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>\n\t\t\t<coverEffectType>0</coverEffectType>\n\t\t\t<coverEffectText><![CDATA[]]></coverEffectText>\n\t\t\t<finderForwardSource><![CDATA[]]></finderForwardSource>\n\t\t\t<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<sourceCommentScene>20</sourceCommentScene>\n\t\t\t<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1753921744492","contextId":"1-1-20-a2bf68d6d15e4d73b99a79f00eff6dfc","shareSrcScene":4}]]></finderShareExtInfo>\n\t\t</finderFeed>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_e3o8s2nf9u2o22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923130, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>1f0d67ec5f3529f160333e2f121b1ada_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_DljUNvBh|v1_loJP5m64</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 5908441516608282730, 'MsgSeq': 871413261}
2025-07-31 08:52:08 | DEBUG | 从群聊消息中提取发送者: wxid_e3o8s2nf9u2o22
2025-07-31 08:52:08 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14712642907883575745</objectId>
			<objectNonceId>6998837226719791102_4_20_13_1_1753922633353692_6ec32c0e-6da7-11f0-91fa-c76ce9a68e23</objectNonceId>
			<feedType>4</feedType>
			<nickname>整活儿小胖橘</nickname>
			<username>v2_060000231003b20faec8c6e58f1ecad5c700e932b077aeb001cd0e915ce822f980272cce55bb@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/Z8nXrOrpYyRd9EQE3R5D6plEIKosjzsVkQoVZA3DJ60Djp75l6DYILQIAqIgicubNUpqQeWvrzHmDmak9HrGQo51bhyWMKSY4wGodzoqrSicAibK4jichsF6cGb8nMDKnXOV1FeO7bJib7U8bXoxjicYeic4g/0]]></avatar>
			<desc>狗子:一想到等会儿要干什么 我就想笑！#搞笑 #狗狗 #万万没想到 #迷惑行为大赏</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>0</authIconType>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eez3Y79SxtvVL0L7CkPM6dFibFeI6caGYwFHqyGNEzNfZnIuEjSBLDQQHqJ6ibTibMw4SWcMGXHodIm0jmhxdibhiabqkr5h3DRM1Rf0WT9u3BQNyTYK65sRWsQ66BYhURJy3k8N84F3s8bvuAg&hy=SH&idx=1&m=7facb544bb33a256e46b012a6032e609&uzid=7a22e&token=6xykWLEnztJD1V02HxcJfWTOeLaomgcSN64fDgQLgjfRdh7EIvItdMLrQVYMHT3JngYnsibYOs386PEnK3VEAqBicaBoMm8XQjQTOHKLQDsod7wLaPJENfzkv8zXEh4obe6uW3ia3cyv5UwoVa5Z2OvRTy6jmpeJJNLhWxXuDpVKrA&basedata=CAESBnhXVDEyOBoGeFdUMTExGgZ4V1QxMTIaBnhXVDEyNhoGeFdUMTEzGgZ4V1QxMjcaBnhXVDEyOCIMCgoKBnhXVDExMhABKgcIvR4QABgC&sign=zGqchF5rnSWarCZzrwqN5E-5JkLL22BxkaF1LqAO4nCgZS1YNcmEQ43krBnSKCJpbdFf7XcJJnDu3Etuf-fh9A&ctsc=20&extg=108b900&ftype=201&svrbypass=AAuL%2FQsFAAABAAAAAAD8azfgas7%2B4575TLyKaBAAAADnaHZTnGbFfAj9RgZXfw6VEjo4ZHYE3hi3B%2FSS7pencRyl44AaMVeyiMoMbnzw0bLwQC74fTmyh9U%3D&svrnonce=1753922636]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvsMKVJgw3kjUibmianEkBPjKwOOAiaeOSG9mkYS72jOqKzZ99HZDrw3zqbxg7jeHq8DvZJNJ76XV6gpasrIWeamkzQzbiafGiaEDickB2y3soDOAEU&hy=SH&idx=1&m=8cea9af5c32f391cd553a14c3fea4b4a&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxhTr34vic2gZkpDkEeBOHiaOCZCZSpCdxD2JBuktP9VfOpfCOQxruGnoVNu7uQh7juufgE5AkZstxTF63Sfo7AaZkDbWT2wiaP2OKr2JQIATv6nluHbb5oREyBwqEuZP3tPFUxqzmbhntpu&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvsMKVJgw3kjUibmianEkBPjKwOOAiaeOSG9mkYS72jOqKzZ99HZDrw3zqbxg7jeHq8DvZJNJ76XV6gpasrIWeamkzQzbiafGiaEDickB2y3soDOAEU&hy=SH&idx=1&m=8cea9af5c32f391cd553a14c3fea4b4a&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxhTr34vic2gZkpDkEeBOHiaOCZCZSpCdxD2JBuktP9VfOpfCOQxruGnoVNu7uQh7juufgE5AkZstxTF63Sfo7AaZkDbWT2wiaP2OKr2JQIATv6nluHbb5oREyBwqEuZP3tPFUxqzmbhntpu&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>15</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1753921744492","contextId":"1-1-20-a2bf68d6d15e4d73b99a79f00eff6dfc","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_e3o8s2nf9u2o22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-31 08:52:08 | DEBUG | XML消息类型: 51
2025-07-31 08:52:08 | DEBUG | XML消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-07-31 08:52:08 | DEBUG | XML消息描述: None
2025-07-31 08:52:08 | DEBUG | 附件信息 totallen: 0
2025-07-31 08:52:08 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-31 08:52:08 | DEBUG | XML消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-07-31 08:52:08 | INFO | 未知的XML消息类型: 51
2025-07-31 08:52:08 | INFO | 消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-07-31 08:52:08 | INFO | 消息描述: None
2025-07-31 08:52:08 | INFO | 消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-07-31 08:52:08 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14712642907883575745</objectId>
			<objectNonceId>6998837226719791102_4_20_13_1_1753922633353692_6ec32c0e-6da7-11f0-91fa-c76ce9a68e23</objectNonceId>
			<feedType>4</feedType>
			<nickname>整活儿小胖橘</nickname>
			<username>v2_060000231003b20faec8c6e58f1ecad5c700e932b077aeb001cd0e915ce822f980272cce55bb@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/Z8nXrOrpYyRd9EQE3R5D6plEIKosjzsVkQoVZA3DJ60Djp75l6DYILQIAqIgicubNUpqQeWvrzHmDmak9HrGQo51bhyWMKSY4wGodzoqrSicAibK4jichsF6cGb8nMDKnXOV1FeO7bJib7U8bXoxjicYeic4g/0]]></avatar>
			<desc>狗子:一想到等会儿要干什么 我就想笑！#搞笑 #狗狗 #万万没想到 #迷惑行为大赏</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>0</authIconType>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eez3Y79SxtvVL0L7CkPM6dFibFeI6caGYwFHqyGNEzNfZnIuEjSBLDQQHqJ6ibTibMw4SWcMGXHodIm0jmhxdibhiabqkr5h3DRM1Rf0WT9u3BQNyTYK65sRWsQ66BYhURJy3k8N84F3s8bvuAg&hy=SH&idx=1&m=7facb544bb33a256e46b012a6032e609&uzid=7a22e&token=6xykWLEnztJD1V02HxcJfWTOeLaomgcSN64fDgQLgjfRdh7EIvItdMLrQVYMHT3JngYnsibYOs386PEnK3VEAqBicaBoMm8XQjQTOHKLQDsod7wLaPJENfzkv8zXEh4obe6uW3ia3cyv5UwoVa5Z2OvRTy6jmpeJJNLhWxXuDpVKrA&basedata=CAESBnhXVDEyOBoGeFdUMTExGgZ4V1QxMTIaBnhXVDEyNhoGeFdUMTEzGgZ4V1QxMjcaBnhXVDEyOCIMCgoKBnhXVDExMhABKgcIvR4QABgC&sign=zGqchF5rnSWarCZzrwqN5E-5JkLL22BxkaF1LqAO4nCgZS1YNcmEQ43krBnSKCJpbdFf7XcJJnDu3Etuf-fh9A&ctsc=20&extg=108b900&ftype=201&svrbypass=AAuL%2FQsFAAABAAAAAAD8azfgas7%2B4575TLyKaBAAAADnaHZTnGbFfAj9RgZXfw6VEjo4ZHYE3hi3B%2FSS7pencRyl44AaMVeyiMoMbnzw0bLwQC74fTmyh9U%3D&svrnonce=1753922636]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvsMKVJgw3kjUibmianEkBPjKwOOAiaeOSG9mkYS72jOqKzZ99HZDrw3zqbxg7jeHq8DvZJNJ76XV6gpasrIWeamkzQzbiafGiaEDickB2y3soDOAEU&hy=SH&idx=1&m=8cea9af5c32f391cd553a14c3fea4b4a&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxhTr34vic2gZkpDkEeBOHiaOCZCZSpCdxD2JBuktP9VfOpfCOQxruGnoVNu7uQh7juufgE5AkZstxTF63Sfo7AaZkDbWT2wiaP2OKr2JQIATv6nluHbb5oREyBwqEuZP3tPFUxqzmbhntpu&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvsMKVJgw3kjUibmianEkBPjKwOOAiaeOSG9mkYS72jOqKzZ99HZDrw3zqbxg7jeHq8DvZJNJ76XV6gpasrIWeamkzQzbiafGiaEDickB2y3soDOAEU&hy=SH&idx=1&m=8cea9af5c32f391cd553a14c3fea4b4a&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxhTr34vic2gZkpDkEeBOHiaOCZCZSpCdxD2JBuktP9VfOpfCOQxruGnoVNu7uQh7juufgE5AkZstxTF63Sfo7AaZkDbWT2wiaP2OKr2JQIATv6nluHbb5oREyBwqEuZP3tPFUxqzmbhntpu&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>15</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1753921744492","contextId":"1-1-20-a2bf68d6d15e4d73b99a79f00eff6dfc","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_e3o8s2nf9u2o22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

