2025-07-31 08:57:31 | SUCCESS | 读取主设置成功
2025-07-31 08:57:31 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-31 08:57:32 | INFO | 2025/07/31 08:57:32 GetRedisAddr: 127.0.0.1:6379
2025-07-31 08:57:32 | INFO | 2025/07/31 08:57:32 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-31 08:57:32 | INFO | 2025/07/31 08:57:32 Server start at :9000
2025-07-31 08:57:32 | SUCCESS | WechatAPI服务已启动
2025-07-31 08:57:33 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-31 08:57:33 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-31 08:57:33 | SUCCESS | 登录成功
2025-07-31 08:57:33 | SUCCESS | 已开启自动心跳
2025-07-31 08:57:33 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-31 08:57:33 | SUCCESS | 数据库初始化成功
2025-07-31 08:57:33 | SUCCESS | 定时任务已启动
2025-07-31 08:57:33 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-31 08:57:33 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-31 08:57:34 | INFO | 播客API初始化成功
2025-07-31 08:57:34 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-31 08:57:34 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-31 08:57:34 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-31 08:57:34 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-31 08:57:34 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-31 08:57:34 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-31 08:57:34 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-31 08:57:34 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-31 08:57:34 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-31 08:57:34 | INFO | [ChatSummary] 数据库初始化成功
2025-07-31 08:57:34 | INFO | [DoubaoVideoSearch] 插件初始化完成
2025-07-31 08:57:34 | DEBUG | [DoubaoVideoSearch] 配置信息:
2025-07-31 08:57:34 | DEBUG |   - 启用状态: True
2025-07-31 08:57:34 | DEBUG |   - 命令列表: ['找视频', '搜视频', '视频搜索']
2025-07-31 08:57:34 | DEBUG |   - 设备ID: 7468716989062841895
2025-07-31 08:57:34 | DEBUG |   - Web ID: 7468716986638386703
2025-07-31 08:57:34 | DEBUG |   - Cookies配置: 已配置
2025-07-31 08:57:34 | DEBUG |   - 令牌桶配置: {'tokens_per_second': 0.5, 'bucket_size': 5}
2025-07-31 08:57:34 | DEBUG |   - 自然化响应: True
2025-07-31 08:57:34 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-31 08:57:34 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-31 08:57:34 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-31 08:57:34 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-31 08:57:34 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-31 08:57:34 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-31 08:57:34 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-31 08:57:34 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-31 08:57:34 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-31 08:57:34 | INFO | [RenameReminder] 开始启用插件...
2025-07-31 08:57:34 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-31 08:57:34 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-31 08:57:34 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-31 08:57:34 | INFO | 已设置检查间隔为 3600 秒
2025-07-31 08:57:34 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-31 08:57:35 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-31 08:57:35 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-31 08:57:35 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-31 08:57:36 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-31 08:57:36 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-31 08:57:36 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-31 08:57:36 | INFO | [yuanbao] 插件初始化完成
2025-07-31 08:57:36 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-31 08:57:36 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-31 08:57:36 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-31 08:57:36 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-31 08:57:36 | INFO | 处理堆积消息中
2025-07-31 08:57:36 | SUCCESS | 处理堆积消息完毕
2025-07-31 08:57:36 | SUCCESS | 开始处理消息
2025-07-31 08:58:17 | DEBUG | 收到消息: {'MsgId': 334963301, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 美女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923499, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_GEuAWdzW|v1_BbgV9Kos</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 美女', 'NewMsgId': 6230923667577072468, 'MsgSeq': 871413262}
2025-07-31 08:58:17 | INFO | 收到文本消息: 消息ID:334963301 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 美女
2025-07-31 08:58:17 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 美女 from wxid_ubbh6q832tcs21
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 开始处理视频搜索请求，用户: wxid_ubbh6q832tcs21
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 用户限制检查 - 用户: wxid_ubbh6q832tcs21, 上次请求: 1753923497.89秒前, 等待时间: 0.00秒
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 令牌桶状态: 5.00/5
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 成功获取令牌，剩余: 4.00
2025-07-31 08:58:17 | INFO | [DoubaoVideoSearch] 开始搜索视频，关键词: '美女'
2025-07-31 08:58:17 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 美女
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 调试信息 - 设备ID: 7468716989062841895, Web ID: 7468716986638386703
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 调试信息 - Cookies长度: 1983
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 第1次尝试，开始构造请求参数
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 生成会话ID: 38175392349789148, 消息ID: ba0d6520-966a-11f0-a188-0d4d3cc8de1f
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 构造搜索查询: 搜索抖音视频:美女
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 请求头: {'Accept': '*/*', 'Accept-Encoding': 'gzip, deflate', 'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7', 'Connection': 'keep-alive', 'Content-Type': 'application/json', 'Cookie': 'i18next=zh; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC43MTQ5MzU5NjkzNjgwNzQxfQ==.yuE0xZkkRp6rqlY66YCqWrFlfN//9LZZZ7S0sT8jJho=; flow_user_country=CN; ttcid=9998073e514b46379ccfd15657ffa06c33; s_v_web_id=verify_mdqh7kzd_ZkQmYYgh_AW2y_4mCs_95OU_aDZp9boGLZ1u; passport_csrf_token=23cc9202dee0bd3af72b1d67305320a1; passport_csrf_token_default=23cc9202dee0bd3af72b1d67305320a1; hook_slardar_session_id=2025073105274644B91E1243B31013D640,ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910866%7Cd0f892d1e4c839a868e20cdb539884dac45992241cb359c30ef040897bbe4427; d_ticket=3ba103731f1417bb3d92f65489c2cf384b9ef; odin_tt=ceee7eb54d94684dae2543622104f0c4c54b16bf7bf53e2998c4381989c54b7a65d33009560a1c9211cc7ee74550542d19171565610c7c33ca884b16e4c1d1b0; n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI; passport_auth_status=1e0467e7ce1de60752facaf3e7239ed9%2C; passport_auth_status_ss=1e0467e7ce1de60752facaf3e7239ed9%2C; sid_guard=53c1e5576dbeb67c1f781749fa771e22%7C1753910897%7C5184000%7CSun%2C+28-Sep-2025+21%3A28%3A17+GMT; uid_tt=b615a4154b708bbf57f415bfbf358f8e; uid_tt_ss=b615a4154b708bbf57f415bfbf358f8e; sid_tt=53c1e5576dbeb67c1f781749fa771e22; sessionid=53c1e5576dbeb67c1f781749fa771e22; sessionid_ss=53c1e5576dbeb67c1f781749fa771e22; session_tlb_tag=sttt%7C6%7CU8HlV22-tnwfeBdJ-nceIv_________SzM3yFwLkszo23AKHohjjcpT0MVXz8bPDuNRWJVotL34%3D; is_staff_user=false; sid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; ssid_ucp_v1=1.0.0-KDg1ZDk2MGUwZGYzM2ExOTUyMjYyZWFjYmRkNWQ4ZmM2MGI0NmY2MjIKHwi8mNDGiq0YEPGcqsQGGMKxHiAMMO6bqsQGOAJA7AcaAmxxIiA1M2MxZTU1NzZkYmViNjdjMWY3ODE3NDlmYTc3MWUyMg; flow_ssr_sidebar_expand=1; ttwid=1%7C6bppB-BmUM4Hin2-liynkLYRkkIDycv0QyinFA2QB1c%7C1753910898%7C7dcd3259bc813de5eb6fcaed0dca1e996cdc228ccac1c0065d0c83eb7d1b697d; passport_fe_beating_status=true; tt_scid=xOJmWMxEZGsbdioWazr17pFNwm8OrYTYLJ2dO01TVpy4KeOvmYDHGWCfSKg1TdFl4dbf', 'Host': 'www.doubao.com', 'Origin': 'https://www.doubao.com', 'Referer': 'https://www.doubao.com/chat/', 'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36', 'x-flow-trace': '04-ea34af681cc347f1-0cb6bf4bd6964895-01', 'last-event-id': 'undefined', 'Agw-Js-Conv': 'str, str', 'X-Requested-With': 'mark.via', 'Sec-Fetch-Site': 'same-origin', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty'}
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 请求体: {"messages": [{"content": "{\"text\": \"\\u641c\\u7d22\\u6296\\u97f3\\u89c6\\u9891:\\u7f8e\\u5973\"}", "content_type": 2001, "attachments": [], "references": []}], "completion_option": {"is_regen": false, "with_suggest": true, "need_create_conversation": false, "launch_stage": 1, "is_replace": false, "is_delete": false, "message_from": 0, "use_deep_think": false, "use_auto_cot": true, "resend_for_regen": false, "event_id": "0"}, "evaluate_option": {"web_ab_params": ""}, "section_id": "381753923449789148", "conversation_id": "38175392349789148", "local_message_id": "ba0d6520-966a-11f0-a188-0d4d3cc8de1f"}
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 请求URL: https://www.doubao.com/samantha/chat/completion?aid=497858&device_id=7468716989062841895&device_plat...
2025-07-31 08:58:17 | DEBUG | [DoubaoVideoSearch] 请求数据大小: 606 字符
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 收到响应，状态码: 200
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 响应头: {'server': 'Tengine', 'content-type': 'text/event-stream', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'date': 'Thu, 31 Jul 2025 00:58:20 GMT', 'cache-control': 'no-cache', 'x-tt-agw-login': '1', 'x-tt-logid': '202507310858203FE86F81471A8E1F1CB1', 'server-timing': 'inner; dur=216,tt_agw; dur=206', 'x-ms-token': 'Y6ecNjcwOqu4VKY30I2VFMV2805kvcZcXwOMkO9_5TabrgdFkHQbhHE8Aclwo4-2gG6X0rFiVKDv5SmT07Mrb6KAo-v6IDmv3aTWPYVn', 'x-envoy-response-flags': '-', 'x-tt-trace-host': '01a904f32dfda387cbf0c81d4c4d78f48a57c7d8f6d38a8c14808bec1094876f8491c78595c821a0a46d144c25c6865986f1e5ce3dc0c1d3b8fe0826c8a498766e7b65144876755657ec26e18a8d2c94eacdf5ab47f65d31a400f63672618f95dc3b46d538a0ae2b0f5e1ac16bf0c206c07541c36155f26d684cd6243ee03ca9d3', 'x-tt-trace-tag': 'id=03;cdn-cache=miss;type=dyn', 'x-tt-trace-id': '00-2507310858203FE86F81471A8E1F1CB1-526FB9096016C4CC-00', 'x-tt-timestamp': '1753923500.749', 'via': 'cache26.cn7023[264,0]', 'timing-allow-origin': '*', 'eagleid': 'db999b2e17539235005061797e'}
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 响应内容长度: 252 字节
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 开始处理响应流数据
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 开始处理SSE响应流
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 响应数据类型: <class 'bytes'>, 大小: 252 字节
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 解码后数据长度: 244 字符
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 处理第 1 个事件，数据长度: 176
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 事件类型: 2005
2025-07-31 08:58:18 | WARNING | [DoubaoVideoSearch] 收到错误事件: {'code': 710020702, 'message': 'system error', 'error_detail': {'code': 710020702, 'locale': 'zh', 'message': '系统错误'}}
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 处理第 2 个事件，数据长度: 52
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 事件类型: 2003
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 收到结束事件
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 结束事件：返回文本结果
2025-07-31 08:58:18 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: text
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 开始处理搜索结果
2025-07-31 08:58:18 | DEBUG | [DoubaoVideoSearch] 开始处理视频消息，结果类型: text
2025-07-31 08:58:18 | WARNING | [DoubaoVideoSearch] 结果类型不是视频: text
2025-07-31 08:58:18 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:这个不行
2025-07-31 08:58:18 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 0.95秒
2025-07-31 08:58:18 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-31 08:58:18 | DEBUG | 处理消息内容: '找视频 美女'
2025-07-31 08:58:18 | DEBUG | 消息内容 '找视频 美女' 不匹配任何命令，忽略
2025-07-31 08:58:44 | DEBUG | 收到消息: {'MsgId': 2022627883, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="032cd0b4629d09fb554cebc1dde6723b" encryver="1" cdnthumbaeskey="032cd0b4629d09fb554cebc1dde6723b" cdnthumburl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688abfc5042438306439646261642d343832622d346263322d626330392d336563313431666335656236020405250a020201000405004c543e00" cdnthumblength="3694" cdnthumbheight="432" cdnthumbwidth="244" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688abfc5042438306439646261642d343832622d346263322d626330392d336563313431666335656236020405250a020201000405004c543e00" length="616701" md5="760f204087535aa6dea45a2e9d489e79" hevc_mid_size="46832" originsourcemd5="b7caba657a51fb2014a8f1b176f01bc5">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjEwNzAwMDIwMTAxMDEyYzAiLCJwZHFIYXNoIjoiZWQ0ZjIwODE3MDNlMDIzZWJm\nZWRjMDg3NDBkZTY0NmU4ZmY1YjkxMDAxOWIzZmU0ZTY2NDlmZTA4MDFhNjZmZiJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923526, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>ab958609c1b0886c30730195f26ecc3e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_MFYrL1ks|v1_/Q2j5NKD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一张图片', 'NewMsgId': 2051451242008084923, 'MsgSeq': 871413265}
2025-07-31 08:58:44 | INFO | 收到图片消息: 消息ID:2022627883 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 XML:<?xml version="1.0"?><msg><img aeskey="032cd0b4629d09fb554cebc1dde6723b" encryver="1" cdnthumbaeskey="032cd0b4629d09fb554cebc1dde6723b" cdnthumburl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688abfc5042438306439646261642d343832622d346263322d626330392d336563313431666335656236020405250a020201000405004c543e00" cdnthumblength="3694" cdnthumbheight="432" cdnthumbwidth="244" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688abfc5042438306439646261642d343832622d346263322d626330392d336563313431666335656236020405250a020201000405004c543e00" length="616701" md5="760f204087535aa6dea45a2e9d489e79" hevc_mid_size="46832" originsourcemd5="b7caba657a51fb2014a8f1b176f01bc5"><secHashInfoBase64>eyJwaGFzaCI6IjEwNzAwMDIwMTAxMDEyYzAiLCJwZHFIYXNoIjoiZWQ0ZjIwODE3MDNlMDIzZWJmZWRjMDg3NDBkZTY0NmU4ZmY1YjkxMDAxOWIzZmU0ZTY2NDlmZTA4MDFhNjZmZiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 08:58:45 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-31 08:58:45 | INFO | [TimerTask] 缓存图片消息: 2022627883
2025-07-31 08:58:45 | DEBUG | 收到消息: {'MsgId': 1215523630, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n已打卡'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923527, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PUCctMTQ|v1_fWJ3DLwn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 已打卡', 'NewMsgId': 4471047109504898512, 'MsgSeq': 871413266}
2025-07-31 08:58:45 | INFO | 收到文本消息: 消息ID:1215523630 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:已打卡
2025-07-31 08:58:46 | DEBUG | 处理消息内容: '已打卡'
2025-07-31 08:58:46 | DEBUG | 消息内容 '已打卡' 不匹配任何命令，忽略
2025-07-31 09:02:03 | DEBUG | 收到消息: {'MsgId': 1279222678, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="8e737034a2a3db9a9ef006a6b4f0ac80" encryver="1" cdnthumbaeskey="8e737034a2a3db9a9ef006a6b4f0ac80" cdnthumburl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688ac08c042464336439616462632d393831342d343935312d613964612d646436303438303834653263020405250a020201000405004c4d9a00" cdnthumblength="3451" cdnthumbheight="432" cdnthumbwidth="244" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688ac08c042464336439616462632d393831342d343935312d613964612d646436303438303834653263020405250a020201000405004c4d9a00" length="888734" md5="d084a78564b3e550228a78a18bd3033e" hevc_mid_size="90850" originsourcemd5="f17f9eee63b632d644e47fe0cc413ea0">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjUwMzA2MDgwMDAwMDIwMTAiLCJwZHFIYXNoIjoiNjJhZDM1YWExNDJhNTY2YjEy\nNTcyYTU2OWE0NWM2OGU3Mjg5YmU4ZDdjNjlkZTU4NmFkMzkyZGJkZWVhMDMxOSJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923725, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>dbb9e165f1f1d69cb38e35b7e8b427c7_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_kTAIqjrg|v1_WoyY24Ph</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一张图片', 'NewMsgId': 7343132588271705725, 'MsgSeq': 871413267}
2025-07-31 09:02:03 | INFO | 收到图片消息: 消息ID:1279222678 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 XML:<?xml version="1.0"?><msg><img aeskey="8e737034a2a3db9a9ef006a6b4f0ac80" encryver="1" cdnthumbaeskey="8e737034a2a3db9a9ef006a6b4f0ac80" cdnthumburl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688ac08c042464336439616462632d393831342d343935312d613964612d646436303438303834653263020405250a020201000405004c4d9a00" cdnthumblength="3451" cdnthumbheight="432" cdnthumbwidth="244" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020468cde53a02032df08e020415b4bc770204688ac08c042464336439616462632d393831342d343935312d613964612d646436303438303834653263020405250a020201000405004c4d9a00" length="888734" md5="d084a78564b3e550228a78a18bd3033e" hevc_mid_size="90850" originsourcemd5="f17f9eee63b632d644e47fe0cc413ea0"><secHashInfoBase64>eyJwaGFzaCI6IjUwMzA2MDgwMDAwMDIwMTAiLCJwZHFIYXNoIjoiNjJhZDM1YWExNDJhNTY2YjEyNTcyYTU2OWE0NWM2OGU3Mjg5YmU4ZDdjNjlkZTU4NmFkMzkyZGJkZWVhMDMxOSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-31 09:02:04 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-31 09:02:04 | INFO | [TimerTask] 缓存图片消息: 1279222678
2025-07-31 09:02:06 | DEBUG | 收到消息: {'MsgId': 2109900266, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n到了到了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753923728, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_9tWFJUmx|v1_dgrZwX5E</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 到了到了', 'NewMsgId': 8477898734643166651, 'MsgSeq': 871413268}
2025-07-31 09:02:06 | INFO | 收到文本消息: 消息ID:2109900266 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:到了到了
2025-07-31 09:02:07 | DEBUG | 处理消息内容: '到了到了'
2025-07-31 09:02:07 | DEBUG | 消息内容 '到了到了' 不匹配任何命令，忽略
